// Script para probar la creación directa de solicitudes
// Ejecutar en la consola del navegador del portal

console.log('🧪 Iniciando test de creación directa de solicitudes...');

// Configuración
const SUPABASE_URL = "https://iexhexcpzjndpgobexnh.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlleGhleGNwempuZHBnb2JleG5oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMjI4MTksImV4cCI6MjA2NTU5ODgxOX0.eH_Ii5SEVkjFyt01hJyyj73Lm2ST5puLLE3CSGxWrHM";

// Test 1: Verificar datos existentes
async function checkExistingData() {
    console.log('📊 Verificando datos existentes...');
    
    try {
        const response = await fetch(`${SUPABASE_URL}/rest/v1/notificaciones_acceso?select=*&order=created_at.desc`, {
            headers: {
                'apikey': SUPABASE_ANON_KEY,
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log(`✅ Encontradas ${data.length} solicitudes existentes:`, data);
            return data;
        } else {
            console.error(`❌ Error al obtener datos: ${response.status}`);
            return [];
        }
    } catch (error) {
        console.error('❌ Error:', error);
        return [];
    }
}

// Test 2: Crear nueva solicitud
async function createDirectRequest() {
    console.log('📝 Creando solicitud directa...');
    
    const testData = {
        empleado_nombre: `Test Directo ${new Date().toLocaleTimeString()}`,
        empleado_cdsid: `DIRECT${Date.now()}`,
        region: 'Valencia',
        plataformas_faltantes: ['Sistema Directo', 'Test Platform'],
        descripcion: 'Solicitud creada directamente para test de sincronización',
        prioridad: 'alta',
        estado: 'pendiente'
    };
    
    try {
        const response = await fetch(`${SUPABASE_URL}/rest/v1/notificaciones_acceso`, {
            method: 'POST',
            headers: {
                'apikey': SUPABASE_ANON_KEY,
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                'Content-Type': 'application/json',
                'Prefer': 'return=representation'
            },
            body: JSON.stringify(testData)
        });
        
        if (response.ok) {
            const result = await response.json();
            console.log('✅ Solicitud creada exitosamente:', result);
            
            // Mostrar notificación visual
            if (typeof window !== 'undefined' && window.alert) {
                alert(`✅ Solicitud creada: ${testData.empleado_nombre}`);
            }
            
            return result[0];
        } else {
            const errorText = await response.text();
            console.error(`❌ Error al crear solicitud: ${response.status} - ${errorText}`);
            return null;
        }
    } catch (error) {
        console.error('❌ Error:', error);
        return null;
    }
}

// Test 3: Verificar si aparece en el DOM
function checkDOMForNotifications() {
    console.log('🔍 Verificando DOM para notificaciones...');
    
    // Buscar elementos que podrían contener notificaciones
    const selectors = [
        '[data-testid*="notification"]',
        '.notification-item',
        '.access-request-item',
        '[class*="notification"]',
        '[class*="request"]',
        'div:contains("Test Directo")',
        'div:contains("DIRECT")'
    ];
    
    let foundElements = 0;
    
    selectors.forEach(selector => {
        try {
            const elements = document.querySelectorAll(selector);
            if (elements.length > 0) {
                console.log(`✅ Encontrados ${elements.length} elementos con selector: ${selector}`);
                foundElements += elements.length;
            }
        } catch (e) {
            // Selector inválido, ignorar
        }
    });
    
    // Buscar texto específico
    const allElements = document.querySelectorAll('*');
    let textMatches = 0;
    
    allElements.forEach(el => {
        if (el.textContent && (
            el.textContent.includes('Test Directo') ||
            el.textContent.includes('DIRECT') ||
            el.textContent.includes('Juan Pérez') ||
            el.textContent.includes('María García')
        )) {
            textMatches++;
        }
    });
    
    console.log(`🔍 Elementos encontrados: ${foundElements}, Coincidencias de texto: ${textMatches}`);
    
    // Verificar tabs específicos
    const tabs = document.querySelectorAll('[role="tab"], [role="tabpanel"]');
    console.log(`📋 Tabs encontrados: ${tabs.length}`);
    
    // Verificar si estamos en la tab correcta
    const activeTab = document.querySelector('[role="tab"][aria-selected="true"]');
    if (activeTab) {
        console.log(`📍 Tab activo: ${activeTab.textContent}`);
    }
    
    return { foundElements, textMatches, tabs: tabs.length };
}

// Test 4: Forzar actualización del portal
function forcePortalRefresh() {
    console.log('🔄 Forzando actualización del portal...');
    
    // Intentar disparar eventos de actualización
    if (typeof window !== 'undefined') {
        // Disparar evento personalizado
        window.dispatchEvent(new CustomEvent('forceRefresh'));
        
        // Intentar recargar datos si hay funciones globales
        if (window.refetchNotifications) {
            window.refetchNotifications();
        }
        
        // Simular click en botón de actualizar
        const refreshButtons = document.querySelectorAll('button:contains("Actualizar"), button:contains("Refrescar"), [aria-label*="refresh"], [aria-label*="actualizar"]');
        refreshButtons.forEach(btn => {
            if (btn.click) {
                btn.click();
                console.log('🔄 Click en botón de actualizar');
            }
        });
    }
}

// Test 5: Verificar localStorage
function checkLocalStorageState() {
    console.log('💾 Verificando estado de localStorage...');
    
    const fordRequests = localStorage.getItem('ford_access_requests');
    
    if (fordRequests) {
        try {
            const requests = JSON.parse(fordRequests);
            console.log(`✅ LocalStorage contiene ${requests.length} solicitudes:`, requests);
            return requests;
        } catch (error) {
            console.error('❌ Error parsing localStorage:', error);
            return [];
        }
    } else {
        console.log('ℹ️ No hay datos en localStorage');
        return [];
    }
}

// Ejecutar todos los tests
async function runCompleteTest() {
    console.log('🚀 Ejecutando test completo de sincronización...');
    console.log('='.repeat(60));
    
    // Test 1: Datos existentes
    const existingData = await checkExistingData();
    console.log('');
    
    // Test 2: Crear nueva solicitud
    const newRequest = await createDirectRequest();
    console.log('');
    
    // Esperar un poco para que se procese
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Test 3: Verificar DOM
    const domResults = checkDOMForNotifications();
    console.log('');
    
    // Test 4: Verificar localStorage
    const localData = checkLocalStorageState();
    console.log('');
    
    // Test 5: Forzar actualización
    forcePortalRefresh();
    console.log('');
    
    // Verificar datos después de la creación
    console.log('🔄 Verificando datos después de la creación...');
    const updatedData = await checkExistingData();
    
    // Resumen
    console.log('📊 RESUMEN DEL TEST:');
    console.log('='.repeat(60));
    console.log(`📋 Datos iniciales: ${existingData.length} solicitudes`);
    console.log(`📝 Nueva solicitud: ${newRequest ? '✅ CREADA' : '❌ FALLÓ'}`);
    console.log(`📋 Datos finales: ${updatedData.length} solicitudes`);
    console.log(`🖥️ Elementos DOM: ${domResults.foundElements} encontrados`);
    console.log(`📝 Coincidencias texto: ${domResults.textMatches} encontradas`);
    console.log(`💾 LocalStorage: ${localData.length} solicitudes`);
    
    const success = newRequest && updatedData.length > existingData.length;
    console.log('');
    console.log(`🎯 RESULTADO GENERAL: ${success ? '✅ ÉXITO' : '❌ PROBLEMA DETECTADO'}`);
    
    if (!success) {
        console.log('');
        console.log('🔧 POSIBLES PROBLEMAS:');
        if (!newRequest) {
            console.log('- ❌ No se pudo crear la solicitud en Supabase');
        }
        if (updatedData.length === existingData.length) {
            console.log('- ❌ Los datos no se actualizaron en Supabase');
        }
        if (domResults.foundElements === 0 && domResults.textMatches === 0) {
            console.log('- ❌ Las notificaciones no aparecen en el DOM');
        }
        if (localData.length === 0) {
            console.log('- ❌ No hay fallback en localStorage');
        }
    }
    
    return {
        existingData,
        newRequest,
        updatedData,
        domResults,
        localData,
        success
    };
}

// Auto-ejecutar si estamos en el navegador
if (typeof window !== 'undefined') {
    // Esperar un poco para que la página cargue
    setTimeout(() => {
        runCompleteTest().then(results => {
            console.log('🏁 Test completo finalizado!');
            
            // Guardar resultados globalmente
            window.testResults = results;
            console.log('💡 Resultados guardados en window.testResults');
        });
    }, 1000);
    
    // Exportar funciones para uso manual
    window.directTests = {
        checkExistingData,
        createDirectRequest,
        checkDOMForNotifications,
        forcePortalRefresh,
        checkLocalStorageState,
        runCompleteTest
    };
    
    console.log('🛠️ Funciones disponibles en window.directTests');
} else {
    console.log('ℹ️ Script cargado pero no en entorno de navegador');
}

// Instrucciones de uso
console.log('');
console.log('📖 INSTRUCCIONES DE USO:');
console.log('1. Abrir el portal en http://localhost:8081');
console.log('2. Ir a la pestaña "Solicitudes Acceso"');
console.log('3. Abrir DevTools (F12) y pegar este script en la consola');
console.log('4. El test se ejecutará automáticamente');
console.log('5. Para ejecutar manualmente: window.directTests.runCompleteTest()');
