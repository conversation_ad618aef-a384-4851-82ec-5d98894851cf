<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Todas las Rutas - Portal MSX</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .route-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .route-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .route-card p {
            margin: 5px 0;
            color: #666;
        }
        .route-url {
            font-family: monospace;
            background: #e9ecef;
            padding: 5px 10px;
            border-radius: 4px;
            color: #495057;
            font-weight: bold;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .test-button {
            background-color: #28a745;
        }
        .test-button:hover {
            background-color: #1e7e34;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status-testing {
            background-color: #fff3cd;
            color: #856404;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧭 Test de Todas las Rutas - Portal MSX</h1>
        <p>Esta página permite probar todas las rutas del portal y verificar que estén funcionando correctamente.</p>
        
        <div style="margin: 20px 0;">
            <button onclick="testAllRoutes()" class="test-button">🚀 Probar Todas las Rutas</button>
            <button onclick="openAllRoutes()">🌐 Abrir Todas las Páginas</button>
            <button onclick="clearLog()">🧹 Limpiar Log</button>
        </div>
    </div>

    <div class="grid">
        <!-- Panel de Rutas -->
        <div class="container">
            <h2>📍 Rutas del Portal</h2>
            
            <div class="route-card">
                <h3>🏠 Página Principal</h3>
                <p class="route-url">http://localhost:8081/</p>
                <p>Dashboard principal con navegación y estadísticas del sistema</p>
                <button onclick="openRoute('/')">Abrir</button>
                <button onclick="testRoute('/', 'Página Principal')" class="test-button">Test</button>
                <span id="status-home" class="status"></span>
            </div>

            <div class="route-card">
                <h3>📊 Dashboard</h3>
                <p class="route-url">http://localhost:8081/dashboard</p>
                <p>Dashboard interno con tabs de solicitudes y estadísticas</p>
                <button onclick="openRoute('/dashboard')">Abrir</button>
                <button onclick="testRoute('/dashboard', 'Dashboard')" class="test-button">Test</button>
                <span id="status-dashboard" class="status"></span>
            </div>

            <div class="route-card">
                <h3>📝 Solicitudes de Acceso</h3>
                <p class="route-url">http://localhost:8081/access-request</p>
                <p>Formulario para crear solicitudes de acceso a sistemas</p>
                <button onclick="openRoute('/access-request')">Abrir</button>
                <button onclick="testRoute('/access-request', 'Solicitudes de Acceso')" class="test-button">Test</button>
                <span id="status-access" class="status"></span>
            </div>

            <div class="route-card">
                <h3>🎫 Tickets de Soporte</h3>
                <p class="route-url">http://localhost:8081/tickets</p>
                <p>Sistema público de tickets de soporte técnico</p>
                <button onclick="openRoute('/tickets')">Abrir</button>
                <button onclick="testRoute('/tickets', 'Tickets de Soporte')" class="test-button">Test</button>
                <span id="status-tickets" class="status"></span>
            </div>

            <div class="route-card">
                <h3>🔐 Admin Login</h3>
                <p class="route-url">http://localhost:8081/admin-login</p>
                <p>Página de autenticación para administradores</p>
                <button onclick="openRoute('/admin-login')">Abrir</button>
                <button onclick="testRoute('/admin-login', 'Admin Login')" class="test-button">Test</button>
                <span id="status-admin-login" class="status"></span>
            </div>

            <div class="route-card">
                <h3>👨‍💼 Admin Dashboard</h3>
                <p class="route-url">http://localhost:8081/admin-dashboard</p>
                <p>Dashboard de administración con gestión completa del sistema</p>
                <button onclick="openRoute('/admin-dashboard')">Abrir</button>
                <button onclick="testRoute('/admin-dashboard', 'Admin Dashboard')" class="test-button">Test</button>
                <span id="status-admin-dashboard" class="status"></span>
            </div>
        </div>

        <!-- Panel de Resultados -->
        <div class="container">
            <h2>📈 Resultados de Tests</h2>
            <div id="test-summary"></div>
            
            <h3>📝 Log de Actividad</h3>
            <div id="activity-log" class="log"></div>
        </div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8081';
        
        const routes = [
            { path: '/', name: 'Página Principal', id: 'home' },
            { path: '/dashboard', name: 'Dashboard', id: 'dashboard' },
            { path: '/access-request', name: 'Solicitudes de Acceso', id: 'access' },
            { path: '/tickets', name: 'Tickets de Soporte', id: 'tickets' },
            { path: '/admin-login', name: 'Admin Login', id: 'admin-login' },
            { path: '/admin-dashboard', name: 'Admin Dashboard', id: 'admin-dashboard' }
        ];

        let testResults = {};

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            
            const logElement = document.getElementById('activity-log');
            logElement.textContent += logEntry + '\n';
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }

        function clearLog() {
            document.getElementById('activity-log').textContent = '';
            testResults = {};
            updateSummary();
            
            // Clear all status indicators
            routes.forEach(route => {
                const statusElement = document.getElementById(`status-${route.id}`);
                if (statusElement) {
                    statusElement.textContent = '';
                    statusElement.className = 'status';
                }
            });
        }

        function updateStatus(routeId, status, message) {
            const statusElement = document.getElementById(`status-${routeId}`);
            if (statusElement) {
                statusElement.textContent = message;
                statusElement.className = `status status-${status}`;
            }
        }

        function updateSummary() {
            const total = Object.keys(testResults).length;
            const passed = Object.values(testResults).filter(r => r.success).length;
            const failed = total - passed;

            const summaryElement = document.getElementById('test-summary');
            summaryElement.innerHTML = `
                <div style="padding: 15px; border-radius: 8px; background: ${passed === total ? '#d4edda' : '#fff3cd'};">
                    <h4>📊 Resumen de Tests</h4>
                    <p><strong>Total:</strong> ${total} rutas</p>
                    <p><strong>✅ Exitosos:</strong> ${passed}</p>
                    <p><strong>❌ Fallidos:</strong> ${failed}</p>
                    <p><strong>📈 Porcentaje:</strong> ${total > 0 ? Math.round((passed / total) * 100) : 0}%</p>
                </div>
            `;
        }

        function openRoute(path) {
            const url = BASE_URL + path;
            log(`🌐 Abriendo ruta: ${url}`);
            window.open(url, '_blank');
        }

        async function testRoute(path, name) {
            const url = BASE_URL + path;
            const routeId = routes.find(r => r.path === path)?.id || path.replace('/', '').replace('-', '');
            
            log(`🧪 Probando ruta: ${name} (${url})`);
            updateStatus(routeId, 'testing', 'Probando...');

            try {
                const response = await fetch(url);
                
                if (response.ok) {
                    log(`✅ ${name}: OK (${response.status})`);
                    updateStatus(routeId, 'success', 'OK');
                    testResults[path] = { success: true, status: response.status };
                } else {
                    log(`❌ ${name}: Error ${response.status}`);
                    updateStatus(routeId, 'error', `Error ${response.status}`);
                    testResults[path] = { success: false, status: response.status };
                }
            } catch (error) {
                log(`❌ ${name}: Error de conexión - ${error.message}`);
                updateStatus(routeId, 'error', 'Error conexión');
                testResults[path] = { success: false, error: error.message };
            }

            updateSummary();
        }

        async function testAllRoutes() {
            log('🚀 Iniciando test de todas las rutas...');
            testResults = {};
            
            for (const route of routes) {
                await testRoute(route.path, route.name);
                // Small delay between tests
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            const total = routes.length;
            const passed = Object.values(testResults).filter(r => r.success).length;
            
            log(`🎯 Test completado: ${passed}/${total} rutas funcionando correctamente`);
            
            if (passed === total) {
                log('🎉 ¡Todas las rutas están funcionando perfectamente!');
            } else {
                log('⚠️ Algunas rutas tienen problemas. Revisa los resultados arriba.');
            }
        }

        function openAllRoutes() {
            log('🌐 Abriendo todas las rutas en pestañas separadas...');
            
            routes.forEach((route, index) => {
                setTimeout(() => {
                    openRoute(route.path);
                }, index * 1000); // 1 second delay between each
            });
            
            log(`📱 Se abrirán ${routes.length} pestañas en ${routes.length} segundos`);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Test de rutas cargado y listo');
            log('💡 Usa los botones para probar rutas individuales o todas a la vez');
            updateSummary();
        });
    </script>
</body>
</html>
