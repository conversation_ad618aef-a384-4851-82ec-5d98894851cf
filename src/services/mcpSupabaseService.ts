// MCP Supabase Service - Local Server Integration
// This service uses the local MCP Supabase server to interact directly with the database
// Project: ffxtpwgrkvicknkjvzgo (local MCP server)

const SUPABASE_PROJECT_ID = 'ffxtpwgrkvicknkjvzgo';
const MCP_SERVER_URL = '/api/supabase'; // Local MCP server endpoint

export interface AccessRequest {
  id: string;
  empleado_nombre: string;
  empleado_cdsid: string;
  region: string;
  plataformas_faltantes: string[];
  descripcion: string | null;
  prioridad: string;
  estado: string | null;
  created_at: string | null;
  updated_at: string | null;
}

class MCPSupabaseService {
  private projectId: string;

  constructor() {
    this.projectId = SUPABASE_PROJECT_ID;
  }

  // Execute SQL query via local MCP server
  private async executeMCPQuery(query: string): Promise<any[]> {
    try {
      console.log('🔍 Executing query via local MCP server:', query);
      
      const response = await fetch(`${MCP_SERVER_URL}/execute_sql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: query
        })
      });

      if (!response.ok) {
        throw new Error(`MCP Server error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('✅ MCP Server response:', result);
      return result.data || result;
      
    } catch (error) {
      console.error('❌ Error executing MCP query:', error);
      throw error;
    }
  }

  // Get all access requests from local MCP Supabase server
  async getAllAccessRequests(): Promise<AccessRequest[]> {
    try {
      console.log('🔍 Fetching all access requests from local MCP Supabase server...');
      
      const query = `
        SELECT id, empleado_nombre, empleado_cdsid, region, plataformas_faltantes, 
               descripcion, prioridad, estado, created_at, updated_at 
        FROM notificaciones_acceso 
        ORDER BY created_at DESC
      `;
      
      const data = await this.executeMCPQuery(query);
      
      // Transform the data to ensure plataformas_faltantes is an array
      const transformedData = data.map(item => ({
        ...item,
        plataformas_faltantes: Array.isArray(item.plataformas_faltantes) 
          ? item.plataformas_faltantes 
          : (item.plataformas_faltantes ? [item.plataformas_faltantes] : [])
      }));
      
      console.log('✅ Successfully fetched', transformedData.length, 'access requests from local MCP server');
      return transformedData;
      
    } catch (error) {
      console.error('❌ Error fetching access requests from local MCP server:', error);
      
      // Return empty array as fallback
      return [];
    }
  }

  // Update access request status via local MCP server
  async updateAccessRequestStatus(requestId: string, newStatus: string): Promise<boolean> {
    try {
      console.log(`🔄 Updating request ${requestId} to status: ${newStatus} via local MCP server`);
      
      const query = `
        UPDATE notificaciones_acceso 
        SET estado = '${newStatus}', updated_at = NOW() 
        WHERE id = '${requestId}'
      `;
      
      await this.executeMCPQuery(query);
      
      console.log('✅ Successfully updated request status via local MCP server');
      return true;
      
    } catch (error) {
      console.error('❌ Error updating request status via local MCP server:', error);
      throw error;
    }
  }

  // Get dashboard statistics from local MCP server
  async getDashboardStats(): Promise<{
    totalRequests: number;
    pendingRequests: number;
    inProcessRequests: number;
    completedRequests: number;
  }> {
    try {
      console.log('📊 Fetching dashboard stats from local MCP server...');
      
      const query = `
        SELECT 
          COUNT(*) as total,
          COUNT(CASE WHEN estado = 'pendiente' THEN 1 END) as pendientes,
          COUNT(CASE WHEN estado = 'en_proceso' THEN 1 END) as en_proceso,
          COUNT(CASE WHEN estado = 'completado' THEN 1 END) as completados
        FROM notificaciones_acceso
      `;
      
      const result = await this.executeMCPQuery(query);
      const statsData = result[0] || { total: 0, pendientes: 0, en_proceso: 0, completados: 0 };
      
      const stats = {
        totalRequests: parseInt(statsData.total) || 0,
        pendingRequests: parseInt(statsData.pendientes) || 0,
        inProcessRequests: parseInt(statsData.en_proceso) || 0,
        completedRequests: parseInt(statsData.completados) || 0,
      };
      
      console.log('✅ Dashboard stats from local MCP server:', stats);
      return stats;
      
    } catch (error) {
      console.error('❌ Error fetching dashboard stats from local MCP server:', error);
      
      // Return empty stats as fallback
      return {
        totalRequests: 0,
        pendingRequests: 0,
        inProcessRequests: 0,
        completedRequests: 0
      };
    }
  }

  // Create new access request via local MCP server
  async createAccessRequest(requestData: Omit<AccessRequest, 'id' | 'created_at' | 'updated_at'>): Promise<AccessRequest> {
    try {
      console.log('📝 Creating new access request via local MCP server...');
      
      const plataformasJson = JSON.stringify(requestData.plataformas_faltantes);
      
      const query = `
        INSERT INTO notificaciones_acceso 
        (empleado_nombre, empleado_cdsid, region, plataformas_faltantes, descripcion, prioridad, estado, created_at, updated_at)
        VALUES (
          '${requestData.empleado_nombre}',
          '${requestData.empleado_cdsid}',
          '${requestData.region}',
          '${plataformasJson}',
          '${requestData.descripcion || ''}',
          '${requestData.prioridad}',
          '${requestData.estado || 'pendiente'}',
          NOW(),
          NOW()
        )
        RETURNING *
      `;
      
      const result = await this.executeMCPQuery(query);
      const newRequest = result[0];
      
      console.log('✅ Successfully created access request via local MCP server');
      return newRequest;
      
    } catch (error) {
      console.error('❌ Error creating access request via local MCP server:', error);
      throw error;
    }
  }

  // Force refresh - gets fresh data from local MCP server
  async forceRefresh(): Promise<AccessRequest[]> {
    console.log('🔄 Force refreshing from local MCP server...');
    return await this.getAllAccessRequests();
  }

  // Real-time sync method using local MCP server
  async syncWithDatabase(): Promise<AccessRequest[]> {
    try {
      console.log('🔄 Syncing with local MCP Supabase server...');
      return await this.forceRefresh();
    } catch (error) {
      console.error('❌ Error syncing with local MCP server:', error);
      throw error;
    }
  }

  // Get recent activity for dashboard from local MCP server
  async getRecentActivity(): Promise<any[]> {
    try {
      const requests = await this.getAllAccessRequests();
      
      // Convert requests to activity items
      const activity = requests
        .sort((a, b) => new Date(b.updated_at || b.created_at || '').getTime() - new Date(a.updated_at || a.created_at || '').getTime())
        .slice(0, 5)
        .map(request => ({
          id: request.id,
          type: 'access_request',
          title: `Solicitud de ${request.empleado_nombre}`,
          description: `${request.empleado_cdsid} - ${request.region}`,
          timestamp: request.updated_at || request.created_at,
          status: request.estado,
          priority: request.prioridad
        }));

      return activity;
    } catch (error) {
      console.error('❌ Error fetching recent activity from local MCP server:', error);
      return [];
    }
  }

  // Delete access request via local MCP server
  async deleteAccessRequest(requestId: string): Promise<boolean> {
    try {
      console.log(`🗑️ Deleting request ${requestId} via local MCP server`);

      const query = `
        DELETE FROM notificaciones_acceso
        WHERE id = '${requestId}'
      `;

      await this.executeMCPQuery(query);

      console.log('✅ Successfully deleted request via local MCP server');
      return true;

    } catch (error) {
      console.error('❌ Error deleting request via local MCP server:', error);
      throw error;
    }
  }

  // Get project info
  getProjectInfo(): { projectId: string; serverType: string } {
    return {
      projectId: this.projectId,
      serverType: 'Local MCP Supabase Server'
    };
  }
}

// Export singleton instance
export const mcpSupabaseService = new MCPSupabaseService();
export default mcpSupabaseService; 