import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3, TrendingUp, Download, Calendar, Filter, FileText, PieChart,
  Activity, Users, Ticket, AlertTriangle, CheckCircle, Clock, Target,
  Zap, RefreshCw, Eye, Settings, TrendingDown, Award, Timer, UserCheck
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useQuery } from '@tanstack/react-query';

interface TicketAnalytics {
  tickets: any[];
  totalTickets: number;
  openTickets: number;
  closedTickets: number;
  inProgressTickets: number;
  resolvedTickets: number;
  criticalTickets: number;
  highPriorityTickets: number;
  avgResolutionTime: number;
  resolutionRate: number;
  dailyTickets: { date: string; count: number; }[];
  categoryDistribution: { name: string; count: number; percentage: number; }[];
  priorityDistribution: { name: string; count: number; percentage: number; }[];
  statusDistribution: { name: string; count: number; percentage: number; }[];
  topAssignees: { name: string; count: number; avgTime: number; }[];
  performanceMetrics: {
    slaCompliance: number;
    customerSatisfaction: number;
    firstResponseTime: number;
    escalationRate: number;
  };
  trends: {
    weeklyGrowth: number;
    monthlyGrowth: number;
    resolutionTrend: number;
    priorityTrend: string;
  };
}

interface TicketFocusedReportsProps {
  projectId: string;
}

export const TicketFocusedReports: React.FC<TicketFocusedReportsProps> = ({ projectId }) => {
  const [dateRange, setDateRange] = useState('30');
  const [reportType, setReportType] = useState('overview');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const { toast } = useToast();

  // Fetch ticket analytics data
  const { data: analytics, isLoading, refetch } = useQuery({
    queryKey: ['ticket-analytics', dateRange, categoryFilter],
    queryFn: async () => {
      console.log('🔄 TicketFocusedReports fetching analytics from Supabase...');
      
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - parseInt(dateRange));

      let query = supabase
        .from('tickets')
        .select('*')
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: false });

      if (categoryFilter !== 'all') {
        query = query.eq('category', categoryFilter);
      }

      const { data: tickets, error } = await query;

      if (error) {
        console.error('❌ TicketFocusedReports Supabase error:', error);
        throw error;
      }

      console.log('✅ TicketFocusedReports received data:', tickets?.length || 0, 'tickets');

      // Calculate analytics
      const analytics = calculateTicketAnalytics(tickets || []);
      
      return analytics;
    },
    refetchInterval: 60000, // 1 minute
    staleTime: 30000, // 30 seconds
  });

  function calculateTicketAnalytics(tickets: any[]): TicketAnalytics {
    const totalTickets = tickets.length;
    const openTickets = tickets.filter(t => t.status === 'open').length;
    const closedTickets = tickets.filter(t => t.status === 'closed').length;
    const inProgressTickets = tickets.filter(t => t.status === 'in_progress').length;
    const resolvedTickets = tickets.filter(t => t.status === 'resolved').length;
    const criticalTickets = tickets.filter(t => t.priority === 'critical').length;
    const highPriorityTickets = tickets.filter(t => t.priority === 'high').length;

    // Calculate average resolution time
    const resolvedWithTime = tickets.filter(t => t.closed_at && t.created_at);
    const avgResolutionTime = resolvedWithTime.length > 0 
      ? resolvedWithTime.reduce((sum, ticket) => {
          const created = new Date(ticket.created_at);
          const closed = new Date(ticket.closed_at);
          return sum + (closed.getTime() - created.getTime());
        }, 0) / resolvedWithTime.length / (1000 * 60 * 60) // hours
      : 0;

    // Calculate resolution rate
    const resolutionRate = totalTickets > 0 
      ? ((closedTickets + resolvedTickets) / totalTickets * 100)
      : 0;

    // Daily tickets trend (last 7 days)
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      return date.toISOString().split('T')[0];
    }).reverse();

    const dailyTickets = last7Days.map(date => ({
      date,
      count: tickets.filter(ticket => ticket.created_at.startsWith(date)).length
    }));

    // Category distribution
    const categoryCount: { [key: string]: number } = {};
    tickets.forEach(ticket => {
      const category = ticket.category || 'Sin categoría';
      categoryCount[category] = (categoryCount[category] || 0) + 1;
    });

    const categoryDistribution = Object.entries(categoryCount)
      .map(([name, count]) => ({
        name,
        count,
        percentage: totalTickets > 0 ? (count / totalTickets * 100) : 0
      }))
      .sort((a, b) => b.count - a.count);

    // Priority distribution
    const priorityCount: { [key: string]: number } = {};
    tickets.forEach(ticket => {
      const priority = ticket.priority || 'medium';
      priorityCount[priority] = (priorityCount[priority] || 0) + 1;
    });

    const priorityDistribution = Object.entries(priorityCount)
      .map(([name, count]) => ({
        name,
        count,
        percentage: totalTickets > 0 ? (count / totalTickets * 100) : 0
      }))
      .sort((a, b) => b.count - a.count);

    // Status distribution
    const statusCount: { [key: string]: number } = {};
    tickets.forEach(ticket => {
      const status = ticket.status || 'open';
      statusCount[status] = (statusCount[status] || 0) + 1;
    });

    const statusDistribution = Object.entries(statusCount)
      .map(([name, count]) => ({
        name,
        count,
        percentage: totalTickets > 0 ? (count / totalTickets * 100) : 0
      }))
      .sort((a, b) => b.count - a.count);

    // Top assignees
    const assigneeCount: { [key: string]: { count: number; totalTime: number; } } = {};
    tickets.forEach(ticket => {
      if (ticket.assigned_to) {
        if (!assigneeCount[ticket.assigned_to]) {
          assigneeCount[ticket.assigned_to] = { count: 0, totalTime: 0 };
        }
        assigneeCount[ticket.assigned_to].count++;
        
        if (ticket.closed_at && ticket.created_at) {
          const created = new Date(ticket.created_at);
          const closed = new Date(ticket.closed_at);
          const timeHours = (closed.getTime() - created.getTime()) / (1000 * 60 * 60);
          assigneeCount[ticket.assigned_to].totalTime += timeHours;
        }
      }
    });

    const topAssignees = Object.entries(assigneeCount)
      .map(([name, data]) => ({
        name,
        count: data.count,
        avgTime: data.count > 0 ? data.totalTime / data.count : 0
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Performance metrics (simulated for demo)
    const performanceMetrics = {
      slaCompliance: Math.min(95, 70 + (resolutionRate * 0.3)),
      customerSatisfaction: Math.min(100, 60 + (resolutionRate * 0.4)),
      firstResponseTime: Math.max(0.5, 4 - (resolutionRate * 0.05)),
      escalationRate: Math.max(0, 15 - (resolutionRate * 0.2))
    };

    // Trends (simulated for demo)
    const trends = {
      weeklyGrowth: Math.random() * 20 - 10, // -10% to +10%
      monthlyGrowth: Math.random() * 30 - 15, // -15% to +15%
      resolutionTrend: resolutionRate > 80 ? 5 : resolutionRate > 60 ? 0 : -5,
      priorityTrend: criticalTickets > highPriorityTickets ? 'increasing' : 'stable'
    };

    return {
      tickets,
      totalTickets,
      openTickets,
      closedTickets,
      inProgressTickets,
      resolvedTickets,
      criticalTickets,
      highPriorityTickets,
      avgResolutionTime,
      resolutionRate,
      dailyTickets,
      categoryDistribution,
      priorityDistribution,
      statusDistribution,
      topAssignees,
      performanceMetrics,
      trends
    };
  }

  const exportReport = (format: 'pdf' | 'excel' | 'csv') => {
    toast({
      title: "Exportando reporte de tickets",
      description: `Generando análisis de tickets en formato ${format.toUpperCase()}...`,
    });
    
    // Aquí iría la lógica de exportación
    setTimeout(() => {
      toast({
        title: "Reporte exportado",
        description: `El análisis de tickets ha sido descargado en formato ${format.toUpperCase()}`,
      });
    }, 2000);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Análisis de Tickets</h2>
          <p className="text-gray-600">Reportes y métricas enfocadas en soporte técnico</p>
        </div>
        
        <div className="flex gap-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Período" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Últimos 7 días</SelectItem>
              <SelectItem value="30">Últimos 30 días</SelectItem>
              <SelectItem value="90">Últimos 3 meses</SelectItem>
              <SelectItem value="365">Último año</SelectItem>
            </SelectContent>
          </Select>

          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Categoría" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todas</SelectItem>
              <SelectItem value="general">General</SelectItem>
              <SelectItem value="network">Red</SelectItem>
              <SelectItem value="software">Software</SelectItem>
              <SelectItem value="hardware">Hardware</SelectItem>
              <SelectItem value="access">Acceso</SelectItem>
              <SelectItem value="system">Sistema</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm" onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualizar
          </Button>
          
          <Button variant="outline" size="sm" onClick={() => exportReport('pdf')}>
            <Download className="h-4 w-4 mr-2" />
            PDF
          </Button>
          
          <Button variant="outline" size="sm" onClick={() => exportReport('excel')}>
            <Download className="h-4 w-4 mr-2" />
            Excel
          </Button>
        </div>
      </div>

      <Tabs value={reportType} onValueChange={setReportType} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Resumen</TabsTrigger>
          <TabsTrigger value="performance">Rendimiento</TabsTrigger>
          <TabsTrigger value="trends">Tendencias</TabsTrigger>
          <TabsTrigger value="team">Equipo</TabsTrigger>
          <TabsTrigger value="detailed">Detallado</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Main KPI Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
              <Card className="border-l-4 border-blue-500">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Total Tickets</p>
                      <p className="text-2xl font-bold text-blue-600">{analytics?.totalTickets || 0}</p>
                      <p className="text-xs text-gray-500">Últimos {dateRange} días</p>
                    </div>
                    <Ticket className="h-8 w-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }}>
              <Card className="border-l-4 border-orange-500">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Abiertos</p>
                      <p className="text-2xl font-bold text-orange-600">{analytics?.openTickets || 0}</p>
                      <p className="text-xs text-gray-500">Requieren atención</p>
                    </div>
                    <AlertTriangle className="h-8 w-8 text-orange-500" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.3 }}>
              <Card className="border-l-4 border-green-500">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Tasa Resolución</p>
                      <p className="text-2xl font-bold text-green-600">{Math.round(analytics?.resolutionRate || 0)}%</p>
                      <p className="text-xs text-gray-500">Cerrados + Resueltos</p>
                    </div>
                    <Target className="h-8 w-8 text-green-500" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.4 }}>
              <Card className="border-l-4 border-purple-500">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Tiempo Promedio</p>
                      <p className="text-2xl font-bold text-purple-600">{Math.round(analytics?.avgResolutionTime || 0)}h</p>
                      <p className="text-xs text-gray-500">Resolución</p>
                    </div>
                    <Timer className="h-8 w-8 text-purple-500" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.5 }}>
              <Card className="border-l-4 border-red-500">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Críticos</p>
                      <p className="text-2xl font-bold text-red-600">{analytics?.criticalTickets || 0}</p>
                      <p className="text-xs text-gray-500">Alta prioridad</p>
                    </div>
                    <AlertTriangle className="h-8 w-8 text-red-500" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.6 }}>
              <Card className="border-l-4 border-blue-500">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">En Progreso</p>
                      <p className="text-2xl font-bold text-blue-600">{analytics?.inProgressTickets || 0}</p>
                      <p className="text-xs text-gray-500">Siendo atendidos</p>
                    </div>
                    <Clock className="h-8 w-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Charts and Analysis */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-blue-500" />
                  Tendencia Últimos 7 Días
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-end gap-2 h-32">
                    {analytics?.dailyTickets.map((day, index) => (
                      <div key={index} className="flex-1 flex flex-col items-center">
                        <div 
                          className="w-full bg-blue-500 rounded-t transition-all duration-300 hover:bg-blue-600"
                          style={{ height: `${Math.max(day.count * 20, 8)}px` }}
                          title={`${day.count} tickets`}
                        ></div>
                        <span className="text-xs text-gray-500 mt-1">
                          {new Date(day.date).getDate()}
                        </span>
                      </div>
                    ))}
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-600">
                      Promedio diario: {Math.round((analytics?.dailyTickets.reduce((sum, day) => sum + day.count, 0) || 0) / 7)} tickets
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5 text-purple-500" />
                  Distribución por Estado
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {analytics?.statusDistribution.slice(0, 5).map((status, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${
                        status.name === 'open' ? 'bg-orange-500' :
                        status.name === 'in_progress' ? 'bg-blue-500' :
                        status.name === 'resolved' ? 'bg-green-500' :
                        status.name === 'closed' ? 'bg-gray-500' :
                        'bg-yellow-500'
                      }`}></div>
                      <span className="text-sm capitalize">{status.name.replace('_', ' ')}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{status.count}</Badge>
                      <span className="text-sm text-gray-500">{Math.round(status.percentage)}%</span>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
