
import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  Plus, 
  Search, 
  Filter,
  Monitor,
  Laptop,
  Smartphone,
  HardDrive,
  Package,
  User,
  MapPin,
  Calendar,
  DollarSign,
  Building
} from 'lucide-react';

interface InventoryItem {
  id: string;
  tipo_equipo: string;
  marca: string;
  modelo: string;
  numero_serie: string;
  estado: 'disponible' | 'asignado' | 'mantenimiento' | 'baja';
  condicion: 'nuevo' | 'bueno' | 'regular' | 'malo';
  ubicacion: string;
  asignado_a_cdsid?: string;
  asignado_a_nombre?: string;
  fecha_asignacion?: string;
  fecha_compra: string;
  garantia_hasta?: string;
  notas?: string;
  costo?: number;
  proveedor?: string;
  created_at: string;
  updated_at: string;
}

export const InventoryTracker = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showNewItemForm, setShowNewItemForm] = useState(false);
  const [newItem, setNewItem] = useState({
    tipo_equipo: '',
    marca: '',
    modelo: '',
    numero_serie: '',
    ubicacion: '',
    fecha_compra: new Date().toISOString().split('T')[0],
    costo: '',
    proveedor: ''
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Obtener inventario
  const { data: inventory = [], isLoading } = useQuery({
    queryKey: ['inventory'],
    queryFn: async () => {
      console.log('🔄 InventoryTracker fetching data from Supabase...');

      const { data, error } = await supabase
        .from('inventario_it')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ InventoryTracker Supabase error:', error);
        // Return empty array if table doesn't exist yet
        if (error.code === 'PGRST116') {
          console.log('ℹ️ Inventory table not found, returning empty array');
          return [];
        }
        throw error;
      }

      console.log('✅ InventoryTracker received data:', data?.length || 0, 'items');
      return data as InventoryItem[];
    },
    refetchInterval: 15000, // 15 segundos
    refetchOnWindowFocus: true,
    staleTime: 10000, // 10 segundos
  });

  // Crear nuevo item
  const createItemMutation = useMutation({
    mutationFn: async (item: any) => {
      const { data, error } = await supabase
        .from('inventario_it')
        .insert([{
          ...item,
          costo: item.costo ? parseFloat(item.costo) : null
        }])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory'] });
      setShowNewItemForm(false);
      setNewItem({
        tipo_equipo: '',
        marca: '',
        modelo: '',
        numero_serie: '',
        ubicacion: '',
        fecha_compra: new Date().toISOString().split('T')[0],
        costo: '',
        proveedor: ''
      });
      toast({
        title: "Equipo agregado exitosamente",
        description: "El nuevo item ha sido registrado en el inventario",
      });
    },
    onError: (error) => {
      console.error('Error creating inventory item:', error);
      toast({
        title: "Error al agregar equipo",
        description: "No se pudo agregar el equipo al inventario. Inténtalo de nuevo.",
        variant: "destructive",
      });
    }
  });

  const handleSubmitItem = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newItem.tipo_equipo || !newItem.marca || !newItem.modelo || !newItem.numero_serie) {
      toast({
        title: "Campos requeridos",
        description: "Por favor completa todos los campos obligatorios",
        variant: "destructive",
      });
      return;
    }
    createItemMutation.mutate(newItem);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'disponible': return 'bg-green-100 text-green-800 border-green-200';
      case 'asignado': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'mantenimiento': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'baja': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case 'nuevo': return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      case 'bueno': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'regular': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'malo': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getEquipmentIcon = (type: string) => {
    const lowerType = type.toLowerCase();
    if (lowerType.includes('laptop') || lowerType.includes('portátil')) return <Laptop className="w-5 h-5" />;
    if (lowerType.includes('monitor') || lowerType.includes('pantalla')) return <Monitor className="w-5 h-5" />;
    if (lowerType.includes('teléfono') || lowerType.includes('móvil')) return <Smartphone className="w-5 h-5" />;
    if (lowerType.includes('disco') || lowerType.includes('storage')) return <HardDrive className="w-5 h-5" />;
    return <Package className="w-5 h-5" />;
  };

  const filteredInventory = inventory.filter(item => {
    const matchesSearch = 
      item.tipo_equipo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.marca.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.modelo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.numero_serie.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.asignado_a_nombre?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false);
    
    const matchesFilter = filterStatus === 'all' || item.estado === filterStatus;
    
    return matchesSearch && matchesFilter;
  });

  const stats = {
    total: inventory.length,
    disponible: inventory.filter(item => item.estado === 'disponible').length,
    asignado: inventory.filter(item => item.estado === 'asignado').length,
    mantenimiento: inventory.filter(item => item.estado === 'mantenimiento').length,
    baja: inventory.filter(item => item.estado === 'baja').length
  };

  return (
    <div className="space-y-8">
      {/* Header y estadísticas */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Inventario IT</h2>
          <p className="text-gray-600 mt-2">Gestiona equipos y activos tecnológicos de la empresa</p>
        </div>
        <Button 
          onClick={() => setShowNewItemForm(true)}
          className="bg-purple-600 hover:bg-purple-700 text-white font-semibold px-6 py-3"
        >
          <Plus className="w-5 h-5 mr-2" />
          Agregar Equipo
        </Button>
      </div>

      {/* Estadísticas rápidas */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card className="text-center">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
            <div className="text-sm text-gray-600">Total</div>
          </CardContent>
        </Card>
        <Card className="text-center">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{stats.disponible}</div>
            <div className="text-sm text-gray-600">Disponibles</div>
          </CardContent>
        </Card>
        <Card className="text-center">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">{stats.asignado}</div>
            <div className="text-sm text-gray-600">Asignados</div>
          </CardContent>
        </Card>
        <Card className="text-center">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">{stats.mantenimiento}</div>
            <div className="text-sm text-gray-600">Mantenimiento</div>
          </CardContent>
        </Card>
        <Card className="text-center">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">{stats.baja}</div>
            <div className="text-sm text-gray-600">De Baja</div>
          </CardContent>
        </Card>
      </div>

      {/* Búsqueda y filtros */}
      <div className="flex gap-4 items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Buscar por tipo, marca, modelo, serie o asignado..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="h-10 px-3 border border-gray-300 rounded-md"
        >
          <option value="all">Todos los estados</option>
          <option value="disponible">Disponible</option>
          <option value="asignado">Asignado</option>
          <option value="mantenimiento">Mantenimiento</option>
          <option value="baja">De Baja</option>
        </select>
      </div>

      {/* Formulario de nuevo equipo */}
      {showNewItemForm && (
        <Card className="border-2 border-purple-200 bg-purple-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-purple-800">
              <Plus className="w-5 h-5" />
              Agregar Nuevo Equipo
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmitItem} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tipo de Equipo *
                  </label>
                  <Input
                    value={newItem.tipo_equipo}
                    onChange={(e) => setNewItem({...newItem, tipo_equipo: e.target.value})}
                    placeholder="Laptop, Monitor, Teléfono IP, etc."
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Marca *
                  </label>
                  <Input
                    value={newItem.marca}
                    onChange={(e) => setNewItem({...newItem, marca: e.target.value})}
                    placeholder="Dell, HP, Cisco, etc."
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Modelo *
                  </label>
                  <Input
                    value={newItem.modelo}
                    onChange={(e) => setNewItem({...newItem, modelo: e.target.value})}
                    placeholder="Latitude 5520, EliteBook 840, etc."
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Número de Serie *
                  </label>
                  <Input
                    value={newItem.numero_serie}
                    onChange={(e) => setNewItem({...newItem, numero_serie: e.target.value})}
                    placeholder="Serial único del equipo"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Ubicación *
                  </label>
                  <Input
                    value={newItem.ubicacion}
                    onChange={(e) => setNewItem({...newItem, ubicacion: e.target.value})}
                    placeholder="Almacén Madrid, Oficina Barcelona, etc."
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Fecha de Compra
                  </label>
                  <Input
                    type="date"
                    value={newItem.fecha_compra}
                    onChange={(e) => setNewItem({...newItem, fecha_compra: e.target.value})}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Costo (€)
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    value={newItem.costo}
                    onChange={(e) => setNewItem({...newItem, costo: e.target.value})}
                    placeholder="850.00"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Proveedor
                  </label>
                  <Input
                    value={newItem.proveedor}
                    onChange={(e) => setNewItem({...newItem, proveedor: e.target.value})}
                    placeholder="Dell España, HP Portugal, etc."
                  />
                </div>
              </div>

              <div className="flex gap-3">
                <Button 
                  type="submit" 
                  disabled={createItemMutation.isPending}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  {createItemMutation.isPending ? 'Agregando...' : 'Agregar Equipo'}
                </Button>
                <Button 
                  type="button" 
                  variant="outline"
                  onClick={() => setShowNewItemForm(false)}
                >
                  Cancelar
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Lista de inventario */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {isLoading ? (
          <div className="col-span-full text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="text-gray-500 mt-4">Cargando inventario...</p>
          </div>
        ) : filteredInventory.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No hay equipos</h3>
            <p className="text-gray-500">No se encontraron equipos que coincidan con tu búsqueda.</p>
          </div>
        ) : (
          filteredInventory.map((item) => (
            <Card key={item.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    {getEquipmentIcon(item.tipo_equipo)}
                    <div>
                      <h3 className="font-semibold text-gray-900">{item.marca} {item.modelo}</h3>
                      <p className="text-sm text-gray-600">{item.tipo_equipo}</p>
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    <Badge className={getStatusColor(item.estado)}>
                      {item.estado.charAt(0).toUpperCase() + item.estado.slice(1)}
                    </Badge>
                    <Badge variant="outline" className={getConditionColor(item.condicion)}>
                      {item.condicion.charAt(0).toUpperCase() + item.condicion.slice(1)}
                    </Badge>
                  </div>
                </div>

                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <Package className="w-4 h-4" />
                    <span className="font-mono">{item.numero_serie}</span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4" />
                    <span>{item.ubicacion}</span>
                  </div>

                  {item.asignado_a_nombre && (
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4" />
                      <span>{item.asignado_a_nombre}</span>
                      {item.asignado_a_cdsid && (
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                          {item.asignado_a_cdsid}
                        </span>
                      )}
                    </div>
                  )}

                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    <span>Comprado: {new Date(item.fecha_compra).toLocaleDateString('es-ES')}</span>
                  </div>

                  {item.costo && (
                    <div className="flex items-center gap-2">
                      <DollarSign className="w-4 h-4" />
                      <span>{item.costo.toFixed(2)}€</span>
                    </div>
                  )}

                  {item.proveedor && (
                    <div className="flex items-center gap-2">
                      <Building className="w-4 h-4" />
                      <span>{item.proveedor}</span>
                    </div>
                  )}

                  {item.notas && (
                    <div className="mt-3 p-2 bg-gray-50 rounded text-xs">
                      <strong>Notas:</strong> {item.notas}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};
