import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Plus, Search, Filter, RefreshCw, Download, Eye, Edit, Trash2,
  Calendar, User, UserCheck, ArrowUpDown, MessageSquare, Ticket,
  Clock, AlertCircle, AlertTriangle, CheckCircle, XCircle, Save
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useQuery, useQueryClient } from '@tanstack/react-query';

interface TicketData {
  id: string;
  title: string;
  description: string;
  status: string;
  priority: string;
  category: string;
  creator_email: string;
  creator_id: string;
  submitter_cdsid: string;
  submitter_email?: string;
  affected_cdsid?: string;
  assigned_to?: string;
  ticket_number: number;
  created_at: string;
  updated_at: string;
  closed_at?: string;
}

interface CompleteTicketManagerProps {
  projectId: string;
}

export const CompleteTicketManager: React.FC<CompleteTicketManagerProps> = ({ projectId }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [selectedTicket, setSelectedTicket] = useState<TicketData | null>(null);
  const [editingTicket, setEditingTicket] = useState<TicketData | null>(null);
  const [deletingTicket, setDeletingTicket] = useState<TicketData | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showDetailDialog, setShowDetailDialog] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  const [newTicketForm, setNewTicketForm] = useState({
    title: '',
    description: '',
    category: 'general',
    priority: 'medium',
    creator_email: '',
    creator_id: '',
    submitter_cdsid: '',
    affected_cdsid: '',
    assigned_to: ''
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch tickets with React Query
  const { data: tickets = [], isLoading, refetch } = useQuery({
    queryKey: ['complete-tickets', sortBy, sortOrder],
    queryFn: async () => {
      console.log('🔄 CompleteTicketManager fetching tickets from Supabase...');
      
      const { data, error } = await supabase
        .from('tickets')
        .select('*')
        .order(sortBy, { ascending: sortOrder === 'asc' });
      
      if (error) {
        console.error('❌ CompleteTicketManager Supabase error:', error);
        throw error;
      }
      
      console.log('✅ CompleteTicketManager received data:', data?.length || 0, 'tickets');
      return data as TicketData[];
    },
    refetchInterval: 30000,
    refetchOnWindowFocus: true,
    staleTime: 10000,
  });

  // Real-time subscriptions
  useEffect(() => {
    console.log('🔄 CompleteTicketManager setting up real-time subscription...');
    
    const channel = supabase
      .channel('complete_tickets_realtime')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'tickets'
        },
        (payload) => {
          console.log('🔔 CompleteTicketManager real-time change:', payload);
          queryClient.invalidateQueries({ queryKey: ['complete-tickets'] });
          
          if (payload.eventType === 'INSERT') {
            const newTicket = payload.new as TicketData;
            toast({
              title: "🆕 Nuevo Ticket",
              description: `Ticket #${newTicket.ticket_number}: ${newTicket.title}`,
              duration: 5000,
            });
          } else if (payload.eventType === 'UPDATE') {
            const updatedTicket = payload.new as TicketData;
            toast({
              title: "🔄 Ticket Actualizado",
              description: `Ticket #${updatedTicket.ticket_number} - Estado: ${updatedTicket.status}`,
              duration: 4000,
            });
          } else if (payload.eventType === 'DELETE') {
            toast({
              title: "🗑️ Ticket Eliminado",
              description: "Un ticket ha sido eliminado",
              duration: 3000,
            });
          }
        }
      )
      .subscribe();

    return () => {
      console.log('🔌 CompleteTicketManager cleaning up real-time subscription...');
      supabase.removeChannel(channel);
    };
  }, [queryClient, toast]);

  // CRUD Functions
  const handleCreateTicket = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      console.log('🔄 Creating new ticket:', newTicketForm);

      const { data, error } = await supabase
        .from('tickets')
        .insert([newTicketForm])
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Ticket created successfully:', data);
      
      toast({
        title: "✅ Ticket Creado",
        description: `Ticket #${data.ticket_number}: ${newTicketForm.title}`,
      });

      setShowCreateDialog(false);
      setNewTicketForm({
        title: '',
        description: '',
        category: 'general',
        priority: 'medium',
        creator_email: '',
        creator_id: '',
        submitter_cdsid: '',
        affected_cdsid: '',
        assigned_to: ''
      });
      
      queryClient.invalidateQueries({ queryKey: ['complete-tickets'] });
    } catch (error) {
      console.error('❌ Error creating ticket:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo crear el ticket",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateTicket = async (updatedTicket: TicketData) => {
    try {
      console.log('🔄 Updating ticket:', updatedTicket);

      const updateData: any = {
        title: updatedTicket.title,
        description: updatedTicket.description,
        category: updatedTicket.category,
        priority: updatedTicket.priority,
        status: updatedTicket.status,
        assigned_to: updatedTicket.assigned_to,
        updated_at: new Date().toISOString()
      };

      if (updatedTicket.status === 'closed' && !updatedTicket.closed_at) {
        updateData.closed_at = new Date().toISOString();
      }

      const { error } = await supabase
        .from('tickets')
        .update(updateData)
        .eq('id', updatedTicket.id);

      if (error) throw error;

      console.log('✅ Ticket updated successfully');
      
      toast({
        title: "✅ Ticket Actualizado",
        description: `Ticket #${updatedTicket.ticket_number} ha sido modificado`,
      });

      setShowEditDialog(false);
      setEditingTicket(null);
      queryClient.invalidateQueries({ queryKey: ['complete-tickets'] });
    } catch (error) {
      console.error('❌ Error updating ticket:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo actualizar el ticket",
        variant: "destructive"
      });
    }
  };

  const handleDeleteTicket = async (ticket: TicketData) => {
    try {
      console.log('🔄 Deleting ticket:', ticket);

      const { error } = await supabase
        .from('tickets')
        .delete()
        .eq('id', ticket.id);

      if (error) throw error;

      console.log('✅ Ticket deleted successfully');
      
      toast({
        title: "✅ Ticket Eliminado",
        description: `Ticket #${ticket.ticket_number} ha sido eliminado`,
      });

      setShowDeleteDialog(false);
      setDeletingTicket(null);
      queryClient.invalidateQueries({ queryKey: ['complete-tickets'] });
    } catch (error) {
      console.error('❌ Error deleting ticket:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo eliminar el ticket",
        variant: "destructive"
      });
    }
  };

  const quickUpdateStatus = async (ticketId: string, newStatus: string) => {
    try {
      const updateData: any = { 
        status: newStatus, 
        updated_at: new Date().toISOString() 
      };
      
      if (newStatus === 'closed') {
        updateData.closed_at = new Date().toISOString();
      }

      const { error } = await supabase
        .from('tickets')
        .update(updateData)
        .eq('id', ticketId);

      if (error) throw error;

      toast({
        title: "✅ Estado Actualizado",
        description: `Ticket actualizado a ${getStatusText(newStatus)}`,
      });
      
      queryClient.invalidateQueries({ queryKey: ['complete-tickets'] });
    } catch (error) {
      console.error('❌ Error updating status:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo actualizar el estado",
        variant: "destructive"
      });
    }
  };

  // Filter tickets
  const filteredTickets = tickets.filter(ticket => {
    const matchesSearch = searchTerm === '' || 
      ticket.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.submitter_cdsid.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.creator_email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || ticket.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || ticket.priority === priorityFilter;
    const matchesCategory = categoryFilter === 'all' || ticket.category === categoryFilter;
    
    return matchesSearch && matchesStatus && matchesPriority && matchesCategory;
  });

  // Helper functions
  const getStatusColor = (status: string) => {
    const colors: { [key: string]: string } = {
      'open': 'bg-orange-100 text-orange-800 border-orange-200',
      'in_progress': 'bg-blue-100 text-blue-800 border-blue-200',
      'resolved': 'bg-green-100 text-green-800 border-green-200',
      'closed': 'bg-gray-100 text-gray-800 border-gray-200',
      'pending': 'bg-yellow-100 text-yellow-800 border-yellow-200'
    };
    return colors[status] || colors.open;
  };

  const getPriorityColor = (priority: string) => {
    const colors: { [key: string]: string } = {
      'critical': 'bg-red-100 text-red-800 border-red-200',
      'high': 'bg-orange-100 text-orange-800 border-orange-200',
      'medium': 'bg-yellow-100 text-yellow-800 border-yellow-200',
      'low': 'bg-green-100 text-green-800 border-green-200'
    };
    return colors[priority] || colors.medium;
  };

  const getStatusIcon = (status: string) => {
    const icons: { [key: string]: any } = {
      'open': AlertCircle,
      'in_progress': Clock,
      'resolved': CheckCircle,
      'closed': XCircle,
      'pending': AlertTriangle
    };
    const IconComponent = icons[status] || AlertCircle;
    return <IconComponent className="h-4 w-4" />;
  };

  const getStatusText = (status: string) => {
    const texts: { [key: string]: string } = {
      'open': 'Abierto',
      'in_progress': 'En Progreso',
      'resolved': 'Resuelto',
      'closed': 'Cerrado',
      'pending': 'Pendiente'
    };
    return texts[status] || 'Desconocido';
  };

  const getPriorityText = (priority: string) => {
    const texts: { [key: string]: string } = {
      'critical': 'Crítica',
      'high': 'Alta',
      'medium': 'Media',
      'low': 'Baja'
    };
    return texts[priority] || 'Media';
  };

  // Calculate statistics
  const stats = {
    total: tickets.length,
    open: tickets.filter(t => t.status === 'open').length,
    inProgress: tickets.filter(t => t.status === 'in_progress').length,
    resolved: tickets.filter(t => t.status === 'resolved').length,
    closed: tickets.filter(t => t.status === 'closed').length,
    critical: tickets.filter(t => t.priority === 'critical').length,
    high: tickets.filter(t => t.priority === 'high').length
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Statistics */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Gestión Completa de Tickets</h2>
          <p className="text-gray-600">Sistema completo de soporte técnico con CRUD</p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualizar
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
          <Button size="sm" className="bg-blue-600 hover:bg-blue-700" onClick={() => setShowCreateDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Nuevo Ticket
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
          <Card className="border-l-4 border-blue-500">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.total}</p>
                </div>
                <Ticket className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }}>
          <Card className="border-l-4 border-orange-500">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Abiertos</p>
                  <p className="text-2xl font-bold text-orange-600">{stats.open}</p>
                </div>
                <AlertCircle className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.3 }}>
          <Card className="border-l-4 border-blue-500">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">En Progreso</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.inProgress}</p>
                </div>
                <Clock className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.4 }}>
          <Card className="border-l-4 border-green-500">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Resueltos</p>
                  <p className="text-2xl font-bold text-green-600">{stats.resolved}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.5 }}>
          <Card className="border-l-4 border-gray-500">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Cerrados</p>
                  <p className="text-2xl font-bold text-gray-600">{stats.closed}</p>
                </div>
                <XCircle className="h-8 w-8 text-gray-500" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.6 }}>
          <Card className="border-l-4 border-red-500">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Críticos</p>
                  <p className="text-2xl font-bold text-red-600">{stats.critical}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.7 }}>
          <Card className="border-l-4 border-orange-500">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Alta Prioridad</p>
                  <p className="text-2xl font-bold text-orange-600">{stats.high}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Buscar tickets..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Estado" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Todos los estados</SelectItem>
            <SelectItem value="open">🟡 Abierto</SelectItem>
            <SelectItem value="in_progress">🔵 En progreso</SelectItem>
            <SelectItem value="resolved">🟢 Resuelto</SelectItem>
            <SelectItem value="closed">⚫ Cerrado</SelectItem>
            <SelectItem value="pending">🟠 Pendiente</SelectItem>
          </SelectContent>
        </Select>

        <Select value={priorityFilter} onValueChange={setPriorityFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Prioridad" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Todas las prioridades</SelectItem>
            <SelectItem value="critical">🔴 Crítica</SelectItem>
            <SelectItem value="high">🟠 Alta</SelectItem>
            <SelectItem value="medium">🟡 Media</SelectItem>
            <SelectItem value="low">🟢 Baja</SelectItem>
          </SelectContent>
        </Select>

        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Categoría" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Todas las categorías</SelectItem>
            <SelectItem value="general">General</SelectItem>
            <SelectItem value="network">Red</SelectItem>
            <SelectItem value="software">Software</SelectItem>
            <SelectItem value="hardware">Hardware</SelectItem>
            <SelectItem value="access">Acceso</SelectItem>
            <SelectItem value="system">Sistema</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Tickets Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <AnimatePresence>
          {filteredTickets.map((ticket) => (
            <motion.div
              key={ticket.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-gray-300 hover:border-blue-500">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-gray-100 rounded-lg">
                        {getStatusIcon(ticket.status)}
                      </div>
                      <div>
                        <CardTitle className="text-lg">{ticket.title}</CardTitle>
                        <p className="text-sm text-gray-600">#{ticket.ticket_number}</p>
                      </div>
                    </div>
                    <div className="flex flex-col gap-1">
                      <Badge className={getStatusColor(ticket.status)}>
                        {getStatusText(ticket.status)}
                      </Badge>
                      <Badge className={getPriorityColor(ticket.priority)}>
                        {getPriorityText(ticket.priority)}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>

                <CardContent>
                  <div className="space-y-3">
                    <p className="text-sm text-gray-600 line-clamp-2">{ticket.description}</p>

                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="text-gray-500">Categoría:</span>
                        <p className="font-medium">{ticket.category}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Creador:</span>
                        <p className="font-medium">{ticket.submitter_cdsid}</p>
                      </div>
                    </div>

                    {ticket.assigned_to && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <User className="h-4 w-4" />
                        Asignado a: {ticket.assigned_to}
                      </div>
                    )}

                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Calendar className="h-4 w-4" />
                      {new Date(ticket.created_at).toLocaleDateString()}
                    </div>

                    <div className="flex gap-2 pt-3">
                      <Button variant="outline" size="sm" onClick={() => {
                        setEditingTicket(ticket);
                        setShowEditDialog(true);
                      }}>
                        <Edit className="h-4 w-4 mr-1" />
                        Editar
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => {
                        setSelectedTicket(ticket);
                        setShowDetailDialog(true);
                      }}>
                        <Eye className="h-4 w-4 mr-1" />
                        Ver
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => {
                        setDeletingTicket(ticket);
                        setShowDeleteDialog(true);
                      }}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* Quick Status Update */}
                    <div className="pt-2 border-t">
                      <p className="text-xs text-gray-500 mb-2">Cambio rápido de estado:</p>
                      <div className="flex gap-1">
                        <Button size="sm" variant="outline" className="text-xs px-2 py-1"
                          onClick={() => quickUpdateStatus(ticket.id, 'in_progress')}>
                          En Progreso
                        </Button>
                        <Button size="sm" variant="outline" className="text-xs px-2 py-1"
                          onClick={() => quickUpdateStatus(ticket.id, 'resolved')}>
                          Resolver
                        </Button>
                        <Button size="sm" variant="outline" className="text-xs px-2 py-1"
                          onClick={() => quickUpdateStatus(ticket.id, 'closed')}>
                          Cerrar
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {filteredTickets.length === 0 && (
        <div className="text-center py-12">
          <Ticket className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No se encontraron tickets</h3>
          <p className="text-gray-500">Intenta ajustar los filtros o crear un nuevo ticket.</p>
        </div>
      )}

      {/* Create Ticket Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Crear Nuevo Ticket</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleCreateTicket} className="space-y-4">
            <div>
              <Label htmlFor="title">Título *</Label>
              <Input
                id="title"
                value={newTicketForm.title}
                onChange={(e) => setNewTicketForm({...newTicketForm, title: e.target.value})}
                placeholder="Descripción breve del problema"
                required
              />
            </div>

            <div>
              <Label htmlFor="description">Descripción *</Label>
              <Textarea
                id="description"
                value={newTicketForm.description}
                onChange={(e) => setNewTicketForm({...newTicketForm, description: e.target.value})}
                placeholder="Descripción detallada del problema..."
                rows={4}
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="category">Categoría *</Label>
                <Select value={newTicketForm.category} onValueChange={(value) => setNewTicketForm({...newTicketForm, category: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Seleccionar categoría" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">General</SelectItem>
                    <SelectItem value="network">Red</SelectItem>
                    <SelectItem value="software">Software</SelectItem>
                    <SelectItem value="hardware">Hardware</SelectItem>
                    <SelectItem value="access">Acceso</SelectItem>
                    <SelectItem value="system">Sistema</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="priority">Prioridad *</Label>
                <Select value={newTicketForm.priority} onValueChange={(value) => setNewTicketForm({...newTicketForm, priority: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Baja</SelectItem>
                    <SelectItem value="medium">Media</SelectItem>
                    <SelectItem value="high">Alta</SelectItem>
                    <SelectItem value="critical">Crítica</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="creator_email">Email del Creador *</Label>
                <Input
                  id="creator_email"
                  type="email"
                  value={newTicketForm.creator_email}
                  onChange={(e) => setNewTicketForm({...newTicketForm, creator_email: e.target.value})}
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              <div>
                <Label htmlFor="submitter_cdsid">CDSID del Solicitante *</Label>
                <Input
                  id="submitter_cdsid"
                  value={newTicketForm.submitter_cdsid}
                  onChange={(e) => setNewTicketForm({...newTicketForm, submitter_cdsid: e.target.value})}
                  placeholder="USR001"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="creator_id">ID del Creador</Label>
                <Input
                  id="creator_id"
                  value={newTicketForm.creator_id}
                  onChange={(e) => setNewTicketForm({...newTicketForm, creator_id: e.target.value})}
                  placeholder="creator-001"
                />
              </div>
              <div>
                <Label htmlFor="assigned_to">Asignar a</Label>
                <Input
                  id="assigned_to"
                  value={newTicketForm.assigned_to}
                  onChange={(e) => setNewTicketForm({...newTicketForm, assigned_to: e.target.value})}
                  placeholder="Nombre del técnico"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="affected_cdsid">CDSID Afectado</Label>
              <Input
                id="affected_cdsid"
                value={newTicketForm.affected_cdsid}
                onChange={(e) => setNewTicketForm({...newTicketForm, affected_cdsid: e.target.value})}
                placeholder="USR002 (si es diferente al solicitante)"
              />
            </div>

            <div className="flex gap-2 pt-4">
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Creando...
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4 mr-2" />
                    Crear Ticket
                  </>
                )}
              </Button>
              <Button type="button" variant="outline" onClick={() => setShowCreateDialog(false)}>
                Cancelar
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Eliminación</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p>¿Estás seguro de que quieres eliminar este ticket?</p>
            {deletingTicket && (
              <div className="p-4 bg-gray-50 rounded-lg">
                <p className="font-medium">#{deletingTicket.ticket_number}: {deletingTicket.title}</p>
                <p className="text-sm text-gray-600">{deletingTicket.description}</p>
                <p className="text-sm text-gray-600">Creado por: {deletingTicket.submitter_cdsid}</p>
              </div>
            )}
            <p className="text-sm text-red-600">Esta acción no se puede deshacer.</p>
            <div className="flex gap-2">
              <Button
                variant="destructive"
                onClick={() => deletingTicket && handleDeleteTicket(deletingTicket)}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Eliminar
              </Button>
              <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
                Cancelar
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
