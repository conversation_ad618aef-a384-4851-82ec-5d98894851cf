import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  Download, 
  Calendar, 
  Filter,
  FileText,
  PieChart,
  Activity,
  Users,
  Package,
  Ticket,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  RefreshCw,
  Eye,
  Settings,
  Target,
  Zap
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useQuery } from '@tanstack/react-query';

interface ReportData {
  accessRequests: any[];
  tickets: any[];
  assets: any[];
}

interface EnhancedReportsManagerProps {
  projectId: string;
}

export const EnhancedReportsManager: React.FC<EnhancedReportsManagerProps> = ({ projectId }) => {
  const [dateRange, setDateRange] = useState('30');
  const [reportType, setReportType] = useState('overview');
  const { toast } = useToast();

  // Fetch all data for reports
  const { data: reportData, isLoading, refetch } = useQuery({
    queryKey: ['reports-data', dateRange],
    queryFn: async () => {
      console.log('🔄 EnhancedReportsManager fetching data from Supabase...');
      
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - parseInt(dateRange));

      const [accessRequestsResponse, ticketsResponse, assetsResponse] = await Promise.all([
        supabase
          .from('notificaciones_acceso')
          .select('*')
          .gte('created_at', startDate.toISOString())
          .order('created_at', { ascending: false }),
        supabase
          .from('tickets')
          .select('*')
          .gte('created_at', startDate.toISOString())
          .order('created_at', { ascending: false }),
        supabase
          .from('assets')
          .select('*')
          .order('created_at', { ascending: false })
      ]);

      const data = {
        accessRequests: accessRequestsResponse.data || [],
        tickets: ticketsResponse.data || [],
        assets: assetsResponse.data || []
      };

      console.log('✅ EnhancedReportsManager received data:', {
        accessRequests: data.accessRequests.length,
        tickets: data.tickets.length,
        assets: data.assets.length
      });

      return data;
    },
    refetchInterval: 60000, // 1 minute
    staleTime: 30000, // 30 seconds
  });

  // Calculate statistics
  const stats = reportData ? {
    // Access Requests Stats
    totalAccessRequests: reportData.accessRequests.length,
    pendingRequests: reportData.accessRequests.filter(r => r.estado === 'pendiente').length,
    approvedRequests: reportData.accessRequests.filter(r => r.estado === 'aprobada').length,
    rejectedRequests: reportData.accessRequests.filter(r => r.estado === 'rechazada').length,
    
    // Tickets Stats
    totalTickets: reportData.tickets.length,
    openTickets: reportData.tickets.filter(t => t.status === 'open').length,
    closedTickets: reportData.tickets.filter(t => t.status === 'closed').length,
    inProgressTickets: reportData.tickets.filter(t => t.status === 'in_progress').length,
    
    // Assets Stats
    totalAssets: reportData.assets.length,
    availableAssets: reportData.assets.filter(a => a.status === 'available').length,
    assignedAssets: reportData.assets.filter(a => a.status === 'assigned').length,
    maintenanceAssets: reportData.assets.filter(a => a.status === 'maintenance').length,
    totalAssetsValue: reportData.assets.reduce((sum, asset) => sum + (asset.purchase_price || 0), 0),
    
    // Performance Metrics
    avgResolutionTime: calculateAvgResolutionTime(reportData.tickets),
    requestApprovalRate: reportData.accessRequests.length > 0 
      ? (reportData.accessRequests.filter(r => r.estado === 'aprobada').length / reportData.accessRequests.length * 100)
      : 0,
    ticketResolutionRate: reportData.tickets.length > 0
      ? (reportData.tickets.filter(t => t.status === 'closed' || t.status === 'resolved').length / reportData.tickets.length * 100)
      : 0,
    
    // Trends
    dailyRequests: calculateDailyTrends(reportData.accessRequests),
    dailyTickets: calculateDailyTrends(reportData.tickets),
    
    // Top Categories
    topRequestRegions: getTopCategories(reportData.accessRequests, 'region'),
    topTicketCategories: getTopCategories(reportData.tickets, 'category'),
    topAssetTypes: getTopCategories(reportData.assets, 'type')
  } : null;

  function calculateAvgResolutionTime(tickets: any[]) {
    const resolvedTickets = tickets.filter(t => t.closed_at && t.created_at);
    if (resolvedTickets.length === 0) return 0;
    
    const totalTime = resolvedTickets.reduce((sum, ticket) => {
      const created = new Date(ticket.created_at);
      const closed = new Date(ticket.closed_at);
      return sum + (closed.getTime() - created.getTime());
    }, 0);
    
    return Math.round(totalTime / resolvedTickets.length / (1000 * 60 * 60)); // hours
  }

  function calculateDailyTrends(items: any[]) {
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      return date.toISOString().split('T')[0];
    }).reverse();

    return last7Days.map(date => ({
      date,
      count: items.filter(item => item.created_at.startsWith(date)).length
    }));
  }

  function getTopCategories(items: any[], field: string) {
    const counts: { [key: string]: number } = {};
    items.forEach(item => {
      const value = item[field] || 'Sin categoría';
      counts[value] = (counts[value] || 0) + 1;
    });
    
    return Object.entries(counts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([name, count]) => ({ name, count }));
  }

  const exportReport = (format: 'pdf' | 'excel' | 'csv') => {
    toast({
      title: "Exportando reporte",
      description: `Generando reporte en formato ${format.toUpperCase()}...`,
    });
    
    // Aquí iría la lógica de exportación
    setTimeout(() => {
      toast({
        title: "Reporte exportado",
        description: `El reporte ha sido descargado en formato ${format.toUpperCase()}`,
      });
    }, 2000);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Reportes y Análisis</h2>
          <p className="text-gray-600">Métricas y estadísticas del sistema</p>
        </div>
        
        <div className="flex gap-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Período" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Últimos 7 días</SelectItem>
              <SelectItem value="30">Últimos 30 días</SelectItem>
              <SelectItem value="90">Últimos 3 meses</SelectItem>
              <SelectItem value="365">Último año</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm" onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualizar
          </Button>
          
          <Button variant="outline" size="sm" onClick={() => exportReport('pdf')}>
            <Download className="h-4 w-4 mr-2" />
            Exportar PDF
          </Button>
          
          <Button variant="outline" size="sm" onClick={() => exportReport('excel')}>
            <Download className="h-4 w-4 mr-2" />
            Excel
          </Button>
        </div>
      </div>

      <Tabs value={reportType} onValueChange={setReportType} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Resumen General</TabsTrigger>
          <TabsTrigger value="requests">Solicitudes</TabsTrigger>
          <TabsTrigger value="tickets">Tickets</TabsTrigger>
          <TabsTrigger value="assets">Activos</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* KPI Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Card className="border-l-4 border-blue-500">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Solicitudes</p>
                      <p className="text-2xl font-bold text-blue-600">{stats?.totalAccessRequests || 0}</p>
                      <p className="text-xs text-gray-500">{stats?.pendingRequests || 0} pendientes</p>
                    </div>
                    <FileText className="h-8 w-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card className="border-l-4 border-green-500">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Tickets</p>
                      <p className="text-2xl font-bold text-green-600">{stats?.totalTickets || 0}</p>
                      <p className="text-xs text-gray-500">{stats?.openTickets || 0} abiertos</p>
                    </div>
                    <Ticket className="h-8 w-8 text-green-500" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <Card className="border-l-4 border-purple-500">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Activos</p>
                      <p className="text-2xl font-bold text-purple-600">{stats?.totalAssets || 0}</p>
                      <p className="text-xs text-gray-500">{stats?.availableAssets || 0} disponibles</p>
                    </div>
                    <Package className="h-8 w-8 text-purple-500" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <Card className="border-l-4 border-orange-500">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Valor Activos</p>
                      <p className="text-2xl font-bold text-orange-600">€{(stats?.totalAssetsValue || 0).toLocaleString()}</p>
                      <p className="text-xs text-gray-500">Total inventario</p>
                    </div>
                    <DollarSign className="h-8 w-8 text-orange-500" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <Card className="border-l-4 border-red-500">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Tasa Aprobación</p>
                      <p className="text-2xl font-bold text-red-600">{Math.round(stats?.requestApprovalRate || 0)}%</p>
                      <p className="text-xs text-gray-500">Solicitudes</p>
                    </div>
                    <Target className="h-8 w-8 text-red-500" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <Card className="border-l-4 border-yellow-500">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Tiempo Resolución</p>
                      <p className="text-2xl font-bold text-yellow-600">{stats?.avgResolutionTime || 0}h</p>
                      <p className="text-xs text-gray-500">Promedio tickets</p>
                    </div>
                    <Clock className="h-8 w-8 text-yellow-500" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Charts and Analysis */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-blue-500" />
                  Tendencias Últimos 7 Días
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Solicitudes de Acceso</h4>
                    <div className="flex items-end gap-2 h-20">
                      {stats?.dailyRequests.map((day, index) => (
                        <div key={index} className="flex-1 flex flex-col items-center">
                          <div 
                            className="w-full bg-blue-500 rounded-t"
                            style={{ height: `${Math.max(day.count * 10, 4)}px` }}
                          ></div>
                          <span className="text-xs text-gray-500 mt-1">
                            {new Date(day.date).getDate()}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Tickets de Soporte</h4>
                    <div className="flex items-end gap-2 h-20">
                      {stats?.dailyTickets.map((day, index) => (
                        <div key={index} className="flex-1 flex flex-col items-center">
                          <div 
                            className="w-full bg-green-500 rounded-t"
                            style={{ height: `${Math.max(day.count * 10, 4)}px` }}
                          ></div>
                          <span className="text-xs text-gray-500 mt-1">
                            {new Date(day.date).getDate()}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5 text-purple-500" />
                  Top Categorías
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Regiones - Solicitudes</h4>
                  <div className="space-y-2">
                    {stats?.topRequestRegions.slice(0, 3).map((region, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm">{region.name}</span>
                        <Badge variant="outline">{region.count}</Badge>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Tipos - Activos</h4>
                  <div className="space-y-2">
                    {stats?.topAssetTypes.slice(0, 3).map((type, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm">{type.name}</span>
                        <Badge variant="outline">{type.count}</Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="requests" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Análisis de Solicitudes de Acceso</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Análisis detallado de solicitudes en desarrollo...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tickets" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Análisis de Tickets</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Análisis detallado de tickets en desarrollo...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="assets" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Análisis de Activos</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Análisis detallado de activos en desarrollo...</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
