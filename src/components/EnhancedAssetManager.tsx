import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Package, 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Eye,
  Download,
  Upload,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Clock,
  MapPin,
  User,
  Calendar,
  DollarSign,
  Wrench,
  Smartphone,
  Monitor,
  Laptop,
  HardDrive,
  Printer,
  Camera,
  Headphones,
  Wifi,
  Server,
  RefreshCw,
  QrCode,
  FileText,
  TrendingUp,
  Activity,
  Settings,
  Archive,
  UserCheck,
  UserX,
  Zap,
  Building,
  Tag
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useQuery, useQueryClient } from '@tanstack/react-query';

interface Asset {
  id: string;
  asset_tag: string;
  name: string;
  category: string;
  type: string;
  brand?: string;
  model?: string;
  serial_number?: string;
  purchase_date?: string;
  purchase_price?: number;
  warranty_expiry?: string;
  status: string;
  condition: string;
  location?: string;
  assigned_to?: string;
  assigned_to_cdsid?: string;
  assigned_date?: string;
  specifications?: any;
  notes?: string;
  last_maintenance?: string;
  next_maintenance?: string;
  created_at: string;
  updated_at: string;
}

interface EnhancedAssetManagerProps {
  projectId: string;
}

export const EnhancedAssetManager: React.FC<EnhancedAssetManagerProps> = ({ projectId }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [locationFilter, setLocationFilter] = useState('all');
  const [showNewAssetDialog, setShowNewAssetDialog] = useState(false);
  const [editingAsset, setEditingAsset] = useState<Asset | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch assets with React Query
  const { data: assets = [], isLoading, refetch } = useQuery({
    queryKey: ['assets'],
    queryFn: async () => {
      console.log('🔄 EnhancedAssetManager fetching assets from Supabase...');
      
      const { data, error } = await supabase
        .from('assets')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('❌ EnhancedAssetManager Supabase error:', error);
        throw error;
      }
      
      console.log('✅ EnhancedAssetManager received data:', data?.length || 0, 'assets');
      return data as Asset[];
    },
    refetchInterval: 30000, // 30 seconds
    refetchOnWindowFocus: true,
    staleTime: 10000, // 10 seconds
  });

  // Real-time subscriptions
  useEffect(() => {
    console.log('🔄 EnhancedAssetManager setting up real-time subscription...');
    
    const channel = supabase
      .channel('enhanced_asset_manager_realtime')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'assets'
        },
        (payload) => {
          console.log('🔔 EnhancedAssetManager real-time change:', payload);
          queryClient.invalidateQueries({ queryKey: ['assets'] });
          
          if (payload.eventType === 'INSERT') {
            const newAsset = payload.new as Asset;
            toast({
              title: "🆕 Nuevo Activo",
              description: `${newAsset.name} ha sido agregado`,
              duration: 5000,
            });
          } else if (payload.eventType === 'UPDATE') {
            const updatedAsset = payload.new as Asset;
            toast({
              title: "🔄 Activo Actualizado",
              description: `${updatedAsset.name} ha sido modificado`,
              duration: 4000,
            });
          }
        }
      )
      .subscribe();

    return () => {
      console.log('🔌 EnhancedAssetManager cleaning up real-time subscription...');
      supabase.removeChannel(channel);
    };
  }, [queryClient, toast]);

  // Get asset icon based on category and type
  const getAssetIcon = (category: string, type: string) => {
    const iconMap: { [key: string]: any } = {
      'IT Equipment': {
        'Laptop': Laptop,
        'Desktop': Monitor,
        'Monitor': Monitor,
        'Printer': Printer,
        'Camera': Camera,
        'Server': Server,
        'default': HardDrive
      },
      'Mobile Device': {
        'Smartphone': Smartphone,
        'Tablet': Smartphone,
        'default': Smartphone
      },
      'Office Equipment': {
        'Printer': Printer,
        'default': Package
      },
      'default': Package
    };

    const categoryIcons = iconMap[category] || iconMap.default;
    const IconComponent = (typeof categoryIcons === 'object' ? categoryIcons[type] || categoryIcons.default : categoryIcons) || Package;
    return <IconComponent className="h-5 w-5" />;
  };

  // Get status color
  const getStatusColor = (status: string) => {
    const colors: { [key: string]: string } = {
      'available': 'bg-green-100 text-green-800 border-green-200',
      'assigned': 'bg-blue-100 text-blue-800 border-blue-200',
      'maintenance': 'bg-yellow-100 text-yellow-800 border-yellow-200',
      'retired': 'bg-red-100 text-red-800 border-red-200',
      'operational': 'bg-purple-100 text-purple-800 border-purple-200'
    };
    return colors[status] || colors.available;
  };

  // Get condition color
  const getConditionColor = (condition: string) => {
    const colors: { [key: string]: string } = {
      'excellent': 'text-green-600',
      'good': 'text-blue-600',
      'fair': 'text-yellow-600',
      'poor': 'text-red-600'
    };
    return colors[condition] || colors.good;
  };

  // Filter assets
  const filteredAssets = assets.filter(asset => {
    const matchesSearch = searchQuery === '' || 
      asset.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      asset.asset_tag.toLowerCase().includes(searchQuery.toLowerCase()) ||
      asset.brand?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      asset.model?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      asset.serial_number?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      asset.assigned_to?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCategory = categoryFilter === 'all' || asset.category === categoryFilter;
    const matchesStatus = statusFilter === 'all' || asset.status === statusFilter;
    const matchesLocation = locationFilter === 'all' || asset.location === locationFilter;

    return matchesSearch && matchesCategory && matchesStatus && matchesLocation;
  });

  // Calculate statistics
  const stats = {
    total: assets.length,
    available: assets.filter(a => a.status === 'available').length,
    assigned: assets.filter(a => a.status === 'assigned').length,
    maintenance: assets.filter(a => a.status === 'maintenance').length,
    retired: assets.filter(a => a.status === 'retired').length,
    laptops: assets.filter(a => a.type === 'Laptop').length,
    monitors: assets.filter(a => a.type === 'Monitor').length,
    smartphones: assets.filter(a => a.type === 'Smartphone').length,
    tablets: assets.filter(a => a.type === 'Tablet').length,
    totalValue: assets.reduce((sum, asset) => sum + (asset.purchase_price || 0), 0),
    avgValue: assets.length > 0 ? assets.reduce((sum, asset) => sum + (asset.purchase_price || 0), 0) / assets.length : 0,
    warrantyExpiring: assets.filter(a => {
      if (!a.warranty_expiry) return false;
      const expiryDate = new Date(a.warranty_expiry);
      const threeMonthsFromNow = new Date();
      threeMonthsFromNow.setMonth(threeMonthsFromNow.getMonth() + 3);
      return expiryDate <= threeMonthsFromNow;
    }).length,
    maintenanceDue: assets.filter(a => {
      if (!a.next_maintenance) return false;
      const maintenanceDate = new Date(a.next_maintenance);
      const today = new Date();
      return maintenanceDate <= today;
    }).length
  };

  // Get unique values for filters
  const categories = [...new Set(assets.map(a => a.category))];
  const locations = [...new Set(assets.map(a => a.location).filter(Boolean))];

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Gestión Avanzada de Activos</h2>
          <p className="text-gray-600">Control completo del inventario empresarial</p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualizar
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            Importar
          </Button>
          <Button size="sm" className="bg-blue-600 hover:bg-blue-700" onClick={() => setShowNewAssetDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Nuevo Activo
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Resumen</TabsTrigger>
          <TabsTrigger value="assets">Activos</TabsTrigger>
          <TabsTrigger value="maintenance">Mantenimiento</TabsTrigger>
          <TabsTrigger value="reports">Reportes</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Statistics Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Card className="border-l-4 border-blue-500">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Total Activos</p>
                      <p className="text-2xl font-bold text-blue-600">{stats.total}</p>
                    </div>
                    <Package className="h-8 w-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card className="border-l-4 border-green-500">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Disponibles</p>
                      <p className="text-2xl font-bold text-green-600">{stats.available}</p>
                    </div>
                    <CheckCircle className="h-8 w-8 text-green-500" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <Card className="border-l-4 border-orange-500">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Asignados</p>
                      <p className="text-2xl font-bold text-orange-600">{stats.assigned}</p>
                    </div>
                    <UserCheck className="h-8 w-8 text-orange-500" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <Card className="border-l-4 border-yellow-500">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Mantenimiento</p>
                      <p className="text-2xl font-bold text-yellow-600">{stats.maintenance}</p>
                    </div>
                    <Wrench className="h-8 w-8 text-yellow-500" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <Card className="border-l-4 border-purple-500">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Valor Total</p>
                      <p className="text-2xl font-bold text-purple-600">€{stats.totalValue.toLocaleString()}</p>
                    </div>
                    <DollarSign className="h-8 w-8 text-purple-500" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <Card className="border-l-4 border-red-500">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Alertas</p>
                      <p className="text-2xl font-bold text-red-600">{stats.warrantyExpiring + stats.maintenanceDue}</p>
                    </div>
                    <AlertTriangle className="h-8 w-8 text-red-500" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Quick Actions and Alerts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-500" />
                  Alertas y Notificaciones
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {stats.warrantyExpiring > 0 && (
                  <div className="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                    <Clock className="h-5 w-5 text-yellow-600" />
                    <div>
                      <p className="font-medium text-yellow-800">Garantías por vencer</p>
                      <p className="text-sm text-yellow-600">{stats.warrantyExpiring} activos con garantía próxima a vencer</p>
                    </div>
                  </div>
                )}
                
                {stats.maintenanceDue > 0 && (
                  <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200">
                    <Wrench className="h-5 w-5 text-red-600" />
                    <div>
                      <p className="font-medium text-red-800">Mantenimiento pendiente</p>
                      <p className="text-sm text-red-600">{stats.maintenanceDue} activos requieren mantenimiento</p>
                    </div>
                  </div>
                )}

                {stats.warrantyExpiring === 0 && stats.maintenanceDue === 0 && (
                  <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <div>
                      <p className="font-medium text-green-800">Todo en orden</p>
                      <p className="text-sm text-green-600">No hay alertas pendientes</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-blue-500" />
                  Distribución por Tipo
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Laptop className="h-4 w-4 text-purple-500" />
                      <span className="text-sm">Laptops</span>
                    </div>
                    <Badge variant="outline">{stats.laptops}</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Monitor className="h-4 w-4 text-blue-500" />
                      <span className="text-sm">Monitores</span>
                    </div>
                    <Badge variant="outline">{stats.monitors}</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Smartphone className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Smartphones</span>
                    </div>
                    <Badge variant="outline">{stats.smartphones}</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Smartphone className="h-4 w-4 text-orange-500" />
                      <span className="text-sm">Tablets</span>
                    </div>
                    <Badge variant="outline">{stats.tablets}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="assets" className="space-y-6">
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Buscar activos..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Categoría" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas las categorías</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Estado" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los estados</SelectItem>
                <SelectItem value="available">Disponible</SelectItem>
                <SelectItem value="assigned">Asignado</SelectItem>
                <SelectItem value="maintenance">Mantenimiento</SelectItem>
                <SelectItem value="operational">Operacional</SelectItem>
                <SelectItem value="retired">Retirado</SelectItem>
              </SelectContent>
            </Select>

            <Select value={locationFilter} onValueChange={setLocationFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Ubicación" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas las ubicaciones</SelectItem>
                {locations.map(location => (
                  <SelectItem key={location} value={location!}>{location}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Assets Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <AnimatePresence>
              {filteredAssets.map((asset) => (
                <motion.div
                  key={asset.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-gray-300 hover:border-blue-500">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-gray-100 rounded-lg">
                            {getAssetIcon(asset.category, asset.type)}
                          </div>
                          <div>
                            <CardTitle className="text-lg">{asset.name}</CardTitle>
                            <p className="text-sm text-gray-600">{asset.asset_tag}</p>
                          </div>
                        </div>
                        <Badge className={getStatusColor(asset.status)}>
                          {asset.status.charAt(0).toUpperCase() + asset.status.slice(1)}
                        </Badge>
                      </div>
                    </CardHeader>
                    
                    <CardContent>
                      <div className="space-y-3">
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <span className="text-gray-500">Marca:</span>
                            <p className="font-medium">{asset.brand || 'N/A'}</p>
                          </div>
                          <div>
                            <span className="text-gray-500">Modelo:</span>
                            <p className="font-medium">{asset.model || 'N/A'}</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <MapPin className="h-4 w-4" />
                          {asset.location || 'Sin ubicación'}
                        </div>

                        {asset.assigned_to && (
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <User className="h-4 w-4" />
                            {asset.assigned_to} ({asset.assigned_to_cdsid})
                          </div>
                        )}

                        <div className="flex items-center gap-2 text-sm">
                          <span className="text-gray-500">Condición:</span>
                          <span className={`font-medium ${getConditionColor(asset.condition)}`}>
                            {asset.condition.charAt(0).toUpperCase() + asset.condition.slice(1)}
                          </span>
                        </div>

                        {asset.purchase_price && (
                          <div className="flex items-center gap-2 text-sm">
                            <DollarSign className="h-4 w-4 text-green-500" />
                            <span className="font-medium text-green-600">€{asset.purchase_price.toLocaleString()}</span>
                          </div>
                        )}
                        
                        <div className="flex gap-2 pt-3">
                          <Button variant="outline" size="sm" onClick={() => setEditingAsset(asset)}>
                            <Edit className="h-4 w-4 mr-1" />
                            Editar
                          </Button>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-1" />
                            Ver
                          </Button>
                          <Button variant="outline" size="sm">
                            <QrCode className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>

          {filteredAssets.length === 0 && (
            <div className="text-center py-12">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No se encontraron activos</h3>
              <p className="text-gray-500">Intenta ajustar los filtros o agregar nuevos activos.</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="maintenance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Programación de Mantenimiento</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Funcionalidad de mantenimiento en desarrollo...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Reportes y Análisis</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Funcionalidad de reportes en desarrollo...</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
