
import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { NotificationForm } from '@/components/NotificationForm';
import { NotificationsDashboard } from '@/components/NotificationsDashboard';
import { TicketingSystem } from '@/components/TicketingSystem';
import { InventoryTracker } from '@/components/InventoryTracker';
import { 
  Lock, 
  Headphones, 
  Monitor,
  Target,
  Briefcase
} from 'lucide-react';

interface NotificationProps {
  notifications: any[];
  isLoading: boolean;
  onAddNotification: (notification: any) => void;
  onDeleteNotification: (id: string) => void;
  onRefresh: () => void;
}

interface ITDashboardTabsProps {
  notificationProps: NotificationProps;
}

export const ITDashboardTabs = ({ notificationProps }: ITDashboardTabsProps) => {
  // Debug logging
  console.log('🔍 ITDashboardTabs render:', {
    notificationProps: notificationProps,
    notificationsLength: notificationProps?.notifications?.length,
    isLoading: notificationProps?.isLoading,
    timestamp: new Date().toISOString()
  });

  const handleClearAll = () => {
    // For now, we'll just show a message that this feature is not implemented
    // In a real app, you might want to implement bulk delete functionality
    console.log('Clear all notifications - feature to be implemented');
  };

  return (
    <Tabs defaultValue="accesos" className="w-full">
      <TabsList className="grid w-full grid-cols-3 mb-8 h-14 bg-white border-2 border-gray-200 shadow-lg rounded-xl p-2">
        <TabsTrigger 
          value="accesos" 
          className="flex items-center gap-3 text-base font-semibold data-[state=active]:bg-blue-500 data-[state=active]:text-white transition-all duration-300"
        >
          <Lock className="w-5 h-5" />
          Gestión de Accesos
        </TabsTrigger>
        <TabsTrigger 
          value="tickets" 
          className="flex items-center gap-3 text-base font-semibold data-[state=active]:bg-green-500 data-[state=active]:text-white transition-all duration-300"
        >
          <Headphones className="w-5 h-5" />
          Sistema de Tickets
        </TabsTrigger>
        <TabsTrigger 
          value="inventario" 
          className="flex items-center gap-3 text-base font-semibold data-[state=active]:bg-purple-500 data-[state=active]:text-white transition-all duration-300"
        >
          <Monitor className="w-5 h-5" />
          Inventario IT
        </TabsTrigger>
      </TabsList>

      <TabsContent value="accesos" className="mt-8">
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-16">
          <div className="space-y-12">
            <div className="section-header text-left">
              <h2 className="section-title text-left flex items-center gap-4">
                <div className="p-3 bg-primary/10 rounded-xl">
                  <Briefcase className="h-8 w-8 text-primary" />
                </div>
                Solicitar Accesos Faltantes
              </h2>
              <p className="section-subtitle text-left">
                Completa el formulario para solicitar acceso a las plataformas del CX Valencia HUB
              </p>
            </div>
            <NotificationForm onAddNotification={notificationProps.onAddNotification} />
          </div>

          <div className="space-y-12">
            <div className="section-header text-left">
              <h2 className="section-title text-left flex items-center gap-4">
                <div className="p-3 bg-blue-50 rounded-xl">
                  <Target className="h-8 w-8 text-blue-600" />
                </div>
                Solicitudes Activas
              </h2>
              <p className="section-subtitle text-left">
                Monitorea el estado de tus solicitudes de acceso en tiempo real
              </p>
            </div>
            <NotificationsDashboard
              notifications={notificationProps.notifications}
              isLoading={notificationProps.isLoading}
              onClearAll={handleClearAll}
              onDeleteNotification={notificationProps.onDeleteNotification}
              onRefresh={notificationProps.onRefresh}
            />
          </div>
        </div>
      </TabsContent>

      <TabsContent value="tickets" className="mt-8">
        <TicketingSystem />
      </TabsContent>

      <TabsContent value="inventario" className="mt-8">
        <InventoryTracker />
      </TabsContent>
    </Tabs>
  );
};
