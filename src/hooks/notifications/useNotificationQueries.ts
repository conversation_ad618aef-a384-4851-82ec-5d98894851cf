import { useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase, handleSupabaseError } from '@/integrations/supabase/client';
import type { Notification, NotificationFilters } from './types';

// Supabase configuration - MSX International Portal
const SUPABASE_URL = "https://iexhexcpzjndpgobexnh.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlleGhleGNwempuZHBnb2JleG5oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMjI4MTksImV4cCI6MjA2NTU5ODgxOX0.eH_Ii5SEVkjFyt01hJyyj73Lm2ST5puLLE3CSGxWrHM";

interface UseNotificationQueriesReturn {
  notifications: Notification[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useNotificationQueries = (filters?: NotificationFilters): UseNotificationQueriesReturn => {
  const {
    data: notifications = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['notifications', filters],
    queryFn: async (): Promise<Notification[]> => {
      console.log('🔄 Fetching notifications...');
      
      try {
        // Try Supabase first
        const { data: supabaseData, error: supabaseError } = await supabase
          .from('notificaciones_acceso')
          .select('*')
          .order('created_at', { ascending: false });

        if (supabaseError) {
          console.warn('⚠️ Supabase query failed, falling back to localStorage:', supabaseError);
          throw supabaseError;
        }

        if (supabaseData && supabaseData.length > 0) {
          console.log('✅ Loaded notifications from Supabase:', supabaseData.length);
          return supabaseData as Notification[];
        }
      } catch (error) {
        console.log('🔧 Loading from localStorage fallback...');
      }

      // Fallback to localStorage
      try {
        const localData = localStorage.getItem('ford_access_requests');
        if (localData) {
          const requests = JSON.parse(localData) as Notification[];
          console.log('✅ Loaded notifications from localStorage:', requests.length);
          return requests.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        }
      } catch (localError) {
        console.error('❌ localStorage fallback failed:', localError);
      }

      // Return empty array if all fails
      console.log('📭 No notifications found');
      return [];
    },
    staleTime: 30000, // 30 seconds
    gcTime: 300000, // 5 minutes
  });

  // Apply filters if provided
  const filteredNotifications = notifications.filter(notification => {
    if (!filters) return true;

    let matches = true;

    if (filters.estado && notification.estado !== filters.estado) {
      matches = false;
    }

    if (filters.prioridad && notification.prioridad !== filters.prioridad) {
      matches = false;
    }

    if (filters.region && notification.region !== filters.region) {
      matches = false;
    }

    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      const searchableFields = [
        notification.empleado_nombre,
        notification.empleado_cdsid,
        notification.descripcion
      ].join(' ').toLowerCase();
      
      if (!searchableFields.includes(searchTerm)) {
        matches = false;
      }
    }

    if (filters.assignedTo && notification.assigned_to !== filters.assignedTo) {
      matches = false;
    }

    if (filters.dateFrom) {
      const notificationDate = new Date(notification.created_at);
      const fromDate = new Date(filters.dateFrom);
      if (notificationDate < fromDate) {
        matches = false;
      }
    }

    if (filters.dateTo) {
      const notificationDate = new Date(notification.created_at);
      const toDate = new Date(filters.dateTo);
      if (notificationDate > toDate) {
        matches = false;
      }
    }

    return matches;
  });

  return {
    notifications: filteredNotifications,
    isLoading,
    error: error?.message || null,
    refetch: () => {
      console.log('🔄 Manual refetch triggered');
      refetch();
    }
  };
};
