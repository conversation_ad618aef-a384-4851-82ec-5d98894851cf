import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import mcpSupabaseService from '@/services/mcpSupabaseService';
import type { Notification, NotificationInput } from './types';

export const useNotificationMutations = () => {
  const queryClient = useQueryClient();

  // Add notification mutation with Supabase integration
  const addNotificationMutation = useMutation({
    mutationFn: async (newNotification: NotificationInput) => {
      console.log('🔄 Creating access request via Supabase:', newNotification);

      try {
        // Create the notification data structure for Supabase
        const notificationData = {
          empleado_nombre: newNotification.empleado_nombre,
          empleado_cdsid: newNotification.empleado_cdsid,
          region: newNotification.region,
          plataformas_faltantes: newNotification.plataformas_faltantes,
          descripcion: newNotification.descripcion || null,
          prioridad: newNotification.prioridad,
          estado: 'pendiente' as const
        };

        console.log('📋 Inserting to Supabase:', notificationData);

        // Try MCP service first for better integration
        try {
          const mcpResult = await mcpSupabaseService.createAccessRequest(notificationData);
          console.log('✅ Access request created via MCP service:', mcpResult);
          return mcpResult;
        } catch (mcpError) {
          console.warn('⚠️ MCP service failed, trying direct Supabase:', mcpError);

          // Fallback to direct Supabase insertion
          const { data, error } = await supabase
            .from('notificaciones_acceso')
            .insert([notificationData])
            .select()
            .single();

          if (error) {
            console.error('❌ Supabase error:', error);
            throw error;
          }

          console.log('✅ Access request created via direct Supabase:', data);
          return data as Notification;
        }

      } catch (error) {
        console.error('❌ Error creating access request:', error);

        // Fallback to localStorage for offline support
        try {
          console.log('🔧 Falling back to localStorage...');
          const newId = `local-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

          const localData = {
            id: newId,
            empleado_nombre: newNotification.empleado_nombre,
            empleado_cdsid: newNotification.empleado_cdsid,
            region: newNotification.region,
            plataformas_faltantes: newNotification.plataformas_faltantes,
            descripcion: newNotification.descripcion,
            prioridad: newNotification.prioridad,
            estado: 'pendiente' as const,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };

          const existingRequests = JSON.parse(localStorage.getItem('ford_access_requests') || '[]');
          existingRequests.push(localData);
          localStorage.setItem('ford_access_requests', JSON.stringify(existingRequests));

          console.log('✅ Access request saved to localStorage:', localData);
          return localData;
        } catch (localError) {
          console.error('❌ localStorage fallback failed:', localError);
          throw new Error('No se pudo crear la solicitud de acceso. Por favor, verifica tu conexión e inténtalo de nuevo.');
        }
      }
    },
    onSuccess: (data) => {
      if (!data) return;
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      toast({
        title: "✅ Solicitud de Acceso Creada",
        description: `Se ha registrado la solicitud para ${data.empleado_nombre}. Tu solicitud será procesada próximamente.`,
      });
    },
    onError: (error: Error) => {
      console.error('❌ Mutation error:', error);
      toast({
        title: "❌ Error al Crear Solicitud",
        description: error.message || "No se pudo crear la solicitud de acceso",
        variant: "destructive",
      });
    }
  });

  // Update notification mutation
  const updateNotificationMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string, updates: Partial<Notification> }) => {
      console.log('🔄 Updating notification via Supabase:', id, updates);

      try {
        // Try MCP service first
        if (updates.estado && typeof updates.estado === 'string') {
          try {
            const success = await mcpSupabaseService.updateAccessRequestStatus(id, updates.estado);
            if (success) {
              console.log('✅ Updated notification via MCP service');
              // Return the updated data by fetching it
              const { data, error } = await supabase
                .from('notificaciones_acceso')
                .select('*')
                .eq('id', id)
                .single();

              if (!error && data) {
                return data as Notification;
              }
            }
          } catch (mcpError) {
            console.warn('⚠️ MCP service update failed, trying direct Supabase:', mcpError);
          }
        }

        // Fallback to direct Supabase update
        const { data, error } = await supabase
          .from('notificaciones_acceso')
          .update({
            ...updates,
            updated_at: new Date().toISOString()
          })
          .eq('id', id)
          .select()
          .single();

        if (error) {
          console.error('❌ Supabase update error:', error);
          throw error;
        }

        console.log('✅ Updated notification via direct Supabase:', data);
        return data as Notification;

      } catch (error) {
        console.error('❌ Error updating notification:', error);

        // Fallback to localStorage for offline support
        try {
          console.log('🔧 Falling back to localStorage update...');
          const requests = JSON.parse(localStorage.getItem('ford_access_requests') || '[]');
          const requestIndex = requests.findIndex((req: any) => req.id === id);

          if (requestIndex !== -1) {
            requests[requestIndex] = {
              ...requests[requestIndex],
              ...updates,
              updated_at: new Date().toISOString()
            };
            localStorage.setItem('ford_access_requests', JSON.stringify(requests));

            console.log('✅ Updated notification in localStorage:', requests[requestIndex]);
            return requests[requestIndex];
          }

          throw new Error('Solicitud no encontrada');
        } catch (localError) {
          console.error('❌ localStorage update failed:', localError);
          throw new Error('No se pudo actualizar la solicitud');
        }
      }
    },
    onSuccess: (data) => {
      if (!data) return;
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      toast({
        title: "✅ Solicitud Actualizada",
        description: `Se ha actualizado la solicitud de ${data.empleado_nombre}`,
      });
    },
    onError: (error: Error) => {
      console.error('❌ Update mutation error:', error);
      toast({
        title: "❌ Error",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Delete notification mutation
  const deleteNotificationMutation = useMutation({
    mutationFn: async (id: string) => {
      console.log('🔄 Deleting notification via Supabase:', id);

      try {
        // Try MCP service first
        try {
          const success = await mcpSupabaseService.deleteAccessRequest(id);
          if (success) {
            console.log('✅ Deleted notification via MCP service');
            return;
          }
        } catch (mcpError) {
          console.warn('⚠️ MCP service delete failed, trying direct Supabase:', mcpError);
        }

        // Fallback to direct Supabase delete
        const { error } = await supabase
          .from('notificaciones_acceso')
          .delete()
          .eq('id', id);

        if (error) {
          console.error('❌ Supabase delete error:', error);
          throw error;
        }

        console.log('✅ Deleted notification via direct Supabase');

      } catch (error) {
        console.error('❌ Error deleting notification:', error);

        // Fallback to localStorage for offline support
        try {
          console.log('🔧 Falling back to localStorage delete...');
          const requests = JSON.parse(localStorage.getItem('ford_access_requests') || '[]');
          const filteredRequests = requests.filter((req: any) => req.id !== id);
          localStorage.setItem('ford_access_requests', JSON.stringify(filteredRequests));

          console.log('✅ Deleted notification from localStorage');
        } catch (localError) {
          console.error('❌ localStorage delete failed:', localError);
          throw new Error('No se pudo eliminar la solicitud');
        }
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      toast({
        title: "✅ Solicitud Eliminada",
        description: "La solicitud ha sido eliminada exitosamente",
      });
    },
    onError: (error: Error) => {
      console.error('❌ Delete mutation error:', error);
      toast({
        title: "❌ Error",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  return {
    addNotification: addNotificationMutation.mutate,
    updateNotification: updateNotificationMutation.mutate,
    deleteNotification: deleteNotificationMutation.mutate,
    isAdding: addNotificationMutation.isPending,
    isUpdating: updateNotificationMutation.isPending,
    isDeleting: deleteNotificationMutation.isPending,
  };
};
