
import { Dispatch, SetStateAction } from 'react';
import { useNotificationQueries } from './notifications/useNotificationQueries';
import { useNotificationMutations } from './notifications/useNotificationMutations';
import { useNotificationRealtime } from './notifications/useNotificationRealtime';
import { useNotificationStats } from './notifications/useNotificationStats';
import type { 
  Notification, 
  NotificationFilters, 
  NotificationStats, 
  NotificationInput 
} from './notifications/types';

interface UseNotificationsReturn {
  notifications: Notification[];
  stats: NotificationStats;
  isLoading: boolean;
  error: string | null;
  addNotification: (data: NotificationInput) => void;
  updateNotification: (data: { id: string; updates: Partial<Notification> }) => void;
  deleteNotification: (id: string) => void;
  refetch: () => void;
  isAdding: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  realTimeEnabled: boolean;
  setRealTimeEnabled: Dispatch<SetStateAction<boolean>>;
}

export const useNotifications = (filters?: NotificationFilters): UseNotificationsReturn => {
  // Use the separate hooks
  const { notifications, isLoading, error, refetch } = useNotificationQueries(filters);
  const { addNotification, updateNotification, deleteNotification, isAdding, isUpdating, isDeleting } = useNotificationMutations();
  const { realTimeEnabled, setRealTimeEnabled } = useNotificationRealtime();
  const stats = useNotificationStats(notifications);

  // Debug logging
  console.log('🔍 useNotifications hook:', {
    notifications: notifications,
    notificationsLength: notifications?.length,
    isLoading,
    error,
    filters,
    timestamp: new Date().toISOString()
  });

  return {
    // Data
    notifications,
    stats,
    isLoading,
    error,
    
    // Actions
    addNotification,
    updateNotification,
    deleteNotification,
    refetch,
    
    // Loading states
    isAdding,
    isUpdating,
    isDeleting,
    
    // Settings
    realTimeEnabled,
    setRealTimeEnabled,
  };
};

// Re-export types for convenience
export type { 
  Notification, 
  NotificationFilters, 
  NotificationStats, 
  NotificationInput 
} from './notifications/types';
