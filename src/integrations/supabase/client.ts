// This file handles Supabase client configuration with environment variables
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types.ts';

// Environment variables with fallbacks for development
// Using MSX International Portal project
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "https://iexhexcpzjndpgobexnh.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlleGhleGNwempuZHBnb2JleG5oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMjI4MTksImV4cCI6MjA2NTU5ODgxOX0.eH_Ii5SEVkjFyt01hJyyj73Lm2ST5puLLE3CSGxWrHM";

// Validate environment variables
if (!SUPABASE_URL || !SUPABASE_PUBLISHABLE_KEY) {
  throw new Error(
    'Missing Supabase environment variables. Please check your .env.local file.'
  );
}

// Create Supabase client with enhanced configuration
export const supabase = createClient<Database>(
  SUPABASE_URL,
  SUPABASE_PUBLISHABLE_KEY,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
    },
    db: {
      schema: 'public',
    },
    global: {
      headers: {
        'X-Application-Name': 'Ford Access Alert Portal',
        'X-Application-Version': import.meta.env.VITE_APP_VERSION || '1.0.0',
      },
    },
  }
);

// Enhanced error handling utility
export const handleSupabaseError = (error: any, context: string = 'Unknown') => {
  console.error(`Supabase Error [${context}]:`, error);
  
  // Return user-friendly error messages
  if (error?.code === 'PGRST116') {
    return 'No se encontraron datos.';
  }
  
  if (error?.code === '23505') {
    return 'Este registro ya existe.';
  }
  
  if (error?.code === '42501') {
    return 'No tienes permisos para realizar esta acción.';
  }
  
  return error?.message || 'Ha ocurrido un error inesperado.';
};

// Connection health check
export const checkSupabaseConnection = async (): Promise<boolean> => {
  try {
    const { error } = await supabase.from('users').select('count').limit(1);
    return !error;
  } catch {
    return false;
  }
};

// Export types for convenience
export type { Database } from './types.ts';