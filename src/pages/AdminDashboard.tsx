import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { theme } from '@/lib/theme';
import { 
  LayoutDashboard, 
  Ticket, 
  Package, 
  Users, 
  UserPlus, 
  Bell, 
  BarChart3, 
  LogOut, 
  Settings,
  Shield,
  Crown,
  Eye,
  AlertTriangle,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle as X,
  RefreshCw,
  Activity,
  Zap,
  FileText,
  Database,
  Monitor,
  Smartphone,
  Laptop,
  HardDrive,
  Wifi,
  Server
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { SuperiorTicketManager } from '@/components/SuperiorTicketManager';
import { EnhancedAssetManager } from '@/components/EnhancedAssetManager';
import { EnhancedReportsManager } from '@/components/EnhancedReportsManager';
import SimpleUserManagement from '@/components/SimpleUserManagement';
import { EnhancedNotifications } from '@/components/EnhancedNotifications';
import { ReportsAnalytics } from '@/components/ReportsAnalytics';
import AccessRequestManager from '@/components/AccessRequestManager';
import mcpSupabaseService from '@/services/mcpSupabaseService';

interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: 'superadmin' | 'admin' | 'viewer';
  active: boolean;
  last_login?: string;
}

interface DashboardStats {
  totalTickets: number;
  openTickets: number;
  closedTickets: number;
  totalUsers: number;
  activeUsers: number;
  totalAssets: number;
  pendingRequests: number;
  totalAccessRequests: number;
  inProcessRequests: number;
  completedRequests: number;
}

const AdminDashboard = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user, signOut } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [stats, setStats] = useState<DashboardStats>({
    totalTickets: 0,
    openTickets: 0,
    closedTickets: 0,
    totalUsers: 0,
    activeUsers: 0,
    totalAssets: 0,
    pendingRequests: 0,
    totalAccessRequests: 0,
    inProcessRequests: 0,
    completedRequests: 0
  });
  const [loading, setLoading] = useState(true);
  const [accessRequests, setAccessRequests] = useState<any[]>([]);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [refreshing, setRefreshing] = useState(false);

  // Unified data loading function using only MCP Supabase service
  const loadDashboardData = useCallback(async (showToast = true) => {
    try {
      console.log('🔄 Loading dashboard data via MCP Supabase cloud...');
      setRefreshing(true);
      
      // Get access requests data directly from Supabase
      const [accessRequestsResponse, ticketsResponse, assetsResponse] = await Promise.all([
        supabase.from('notificaciones_acceso').select('*').order('created_at', { ascending: false }),
        supabase.from('tickets').select('*').order('created_at', { ascending: false }),
        supabase.from('assets').select('*').order('created_at', { ascending: false })
      ]);

      const accessRequestsData = accessRequestsResponse.data || [];
      const ticketsData = ticketsResponse.data || [];
      const assetsData = assetsResponse.data || [];

      // Calculate dashboard stats
      const dashboardStats = {
        totalRequests: accessRequestsData.length,
        pendingRequests: accessRequestsData.filter(r => r.estado === 'pendiente').length,
        approvedRequests: accessRequestsData.filter(r => r.estado === 'aprobada').length,
        rejectedRequests: accessRequestsData.filter(r => r.estado === 'rechazada').length,
        totalTickets: ticketsData.length,
        openTickets: ticketsData.filter(t => t.status === 'open').length,
        closedTickets: ticketsData.filter(t => t.status === 'closed').length
      };
      
      // Update access requests state
      setAccessRequests(accessRequestsData);
      
      // Get users data from Supabase (fallback)
      let usersData = [];
      try {
        const { data: users, error: usersError } = await supabase
          .from('admin_users')
          .select('active');
        
        if (usersError) {
          console.warn('⚠️ Error fetching users:', usersError);
          usersData = [{ active: true }]; // Current user fallback
        } else {
          usersData = users || [{ active: true }];
        }
      } catch (error) {
        console.warn('⚠️ Users query failed:', error);
        usersData = [{ active: true }];
      }
      
      // Calculate comprehensive stats
      const newStats: DashboardStats = {
        // Tickets stats
        totalTickets: ticketsData.length,
        openTickets: ticketsData.filter(t => t.status !== 'closed').length,
        closedTickets: ticketsData.filter(t => t.status === 'closed').length,
        
        // Users stats
        totalUsers: usersData.length,
        activeUsers: usersData.filter(u => u.active).length,
        
        // Assets stats from real data
        totalAssets: assetsData.length,
        
        // Access requests stats from calculated data
        totalAccessRequests: dashboardStats.totalRequests,
        pendingRequests: dashboardStats.pendingRequests,
        inProcessRequests: 0, // No hay estado "en proceso" definido
        completedRequests: dashboardStats.approvedRequests + dashboardStats.rejectedRequests
      };
      
      setStats(newStats);
      setLastUpdate(new Date());
      
      console.log('✅ Dashboard data loaded successfully:', {
        accessRequests: accessRequestsData.length,
        accessRequestsData: accessRequestsData,
        ticketsData: ticketsData,
        dashboardStats: dashboardStats,
        newStats: newStats,
        pending: newStats.pendingRequests,
        inProcess: newStats.inProcessRequests,
        completed: newStats.completedRequests,
        tickets: newStats.totalTickets
      });
      
      if (showToast) {
        toast({
          title: "✅ Dashboard Actualizado",
          description: `${newStats.totalAccessRequests} solicitudes de acceso (${newStats.pendingRequests} pendientes) • ${newStats.totalTickets} tickets`,
        });
      }
      
    } catch (error) {
      console.error('❌ Error loading dashboard data:', error);
      
      toast({
        title: "⚠️ Error de Conexión",
        description: "No se pudo cargar los datos del dashboard",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [toast]);

  // Manual refresh function
  const handleRefresh = useCallback(async () => {
    console.log('🔄 Manual refresh triggered...');
    await loadDashboardData(true);
  }, [loadDashboardData]);

  // Initial load and periodic updates
  useEffect(() => {
    // Initial load
    loadDashboardData(false);

    // Set up periodic refresh every 30 seconds for better responsiveness
    const refreshInterval = setInterval(() => {
      console.log('⏰ Periodic dashboard refresh...');
      loadDashboardData(false);
    }, 30000); // 30 seconds for better responsiveness

    return () => {
      clearInterval(refreshInterval);
    };
  }, [loadDashboardData]);

  // Real-time subscriptions for immediate updates
  useEffect(() => {
    console.log('🔄 AdminDashboard setting up real-time subscriptions...');

    // Subscribe to access requests changes
    const accessRequestsChannel = supabase
      .channel('admin_dashboard_access_requests')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notificaciones_acceso'
        },
        (payload) => {
          console.log('🔔 AdminDashboard access request change:', payload);
          // Reload data immediately when changes occur
          loadDashboardData(false);
        }
      )
      .subscribe();

    // Subscribe to tickets changes
    const ticketsChannel = supabase
      .channel('admin_dashboard_tickets')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'tickets'
        },
        (payload) => {
          console.log('🔔 AdminDashboard ticket change:', payload);
          // Reload data immediately when changes occur
          loadDashboardData(false);
        }
      )
      .subscribe();

    // Subscribe to assets changes
    const assetsChannel = supabase
      .channel('admin_dashboard_assets')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'assets'
        },
        (payload) => {
          console.log('🔔 AdminDashboard asset change:', payload);
          // Reload data immediately when changes occur
          loadDashboardData(false);
        }
      )
      .subscribe();

    return () => {
      console.log('🔌 AdminDashboard cleaning up real-time subscriptions...');
      supabase.removeChannel(accessRequestsChannel);
      supabase.removeChannel(ticketsChannel);
      supabase.removeChannel(assetsChannel);
    };
  }, [loadDashboardData]);

  const handleLogout = () => {
    signOut();
    navigate('/admin-login');
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'superadmin': return <Crown className="h-4 w-4" />;
      case 'admin': return <Shield className="h-4 w-4" />;
      default: return <Eye className="h-4 w-4" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'superadmin': return 'text-yellow-600';
      case 'admin': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const canAccessTab = (tab: string) => {
    if (!user) return false;
    
    switch (tab) {
      case 'users':
        return user.role === 'superadmin';
      case 'notifications':
      case 'reports':
        return user.role === 'superadmin' || user.role === 'admin';
      default:
        return true;
    }
  };

  const getAvailableTabs = () => {
    const tabs = [
      { id: 'overview', label: 'Dashboard', icon: LayoutDashboard },
      { id: 'access-requests', label: 'Solicitudes de Acceso', icon: UserPlus },
      { id: 'tickets', label: 'Tickets', icon: Ticket },
      { id: 'assets', label: 'Activos', icon: Package },
    ];

    if (canAccessTab('notifications')) {
      tabs.push({ id: 'notifications', label: 'Notificaciones', icon: Bell });
    }

    if (canAccessTab('users')) {
      tabs.push({ id: 'users', label: 'Usuarios', icon: Users });
    }

    if (canAccessTab('reports')) {
      tabs.push({ id: 'reports', label: 'Reportes', icon: BarChart3 });
    }

    return tabs;
  };

  const handleTabChange = (value: string) => {
    if (canAccessTab(value) || ['overview', 'access-requests', 'tickets', 'assets'].includes(value)) {
      setActiveTab(value);
    }
  };

  // Recent Activity Component
  const RecentActivityList = () => {
    const recentActivity = accessRequests
      .sort((a, b) => new Date(b.updated_at || b.created_at || '').getTime() - new Date(a.updated_at || a.created_at || '').getTime())
      .slice(0, 5)
      .map(request => ({
        id: request.id,
        type: 'access_request',
        title: `Solicitud de ${request.empleado_nombre}`,
        description: `${request.empleado_cdsid} - ${request.region}`,
        timestamp: request.updated_at || request.created_at,
        status: request.estado,
        priority: request.prioridad
      }));

    const getStatusColor = (status: string) => {
      switch (status) {
        case 'pendiente': return 'text-yellow-600 bg-yellow-50';
        case 'en_proceso': return 'text-blue-600 bg-blue-50';
        case 'completado': return 'text-green-600 bg-green-50';
        case 'rechazado': return 'text-red-600 bg-red-50';
        default: return 'text-gray-600 bg-gray-50';
      }
    };

    const getPriorityColor = (priority: string) => {
      switch (priority) {
        case 'alta': return 'text-red-600';
        case 'media': return 'text-yellow-600';
        case 'baja': return 'text-green-600';
        default: return 'text-gray-600';
      }
    };

    const formatTimeAgo = (timestamp: string) => {
      const now = new Date();
      const time = new Date(timestamp);
      const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));
      
      if (diffInMinutes < 1) return 'Hace un momento';
      if (diffInMinutes < 60) return `Hace ${diffInMinutes} min`;
      if (diffInMinutes < 1440) return `Hace ${Math.floor(diffInMinutes / 60)} h`;
      return `Hace ${Math.floor(diffInMinutes / 1440)} días`;
    };

    if (recentActivity.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p>No hay actividad reciente</p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {recentActivity.map((activity, index) => (
          <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg border hover:bg-gray-50 transition-colors">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                <UserPlus className="h-4 w-4 text-blue-600" />
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="text-sm font-medium text-gray-900 truncate">{activity.title}</h4>
                <Badge variant="outline" className={`text-xs ${getStatusColor(activity.status)}`}>
                  {activity.status}
                </Badge>
              </div>
              <p className="text-sm text-gray-600 mb-1">{activity.description}</p>
              <div className="flex items-center gap-3 text-xs text-gray-500">
                <span>{formatTimeAgo(activity.timestamp)}</span>
                <span className={`font-medium ${getPriorityColor(activity.priority)}`}>
                  Prioridad: {activity.priority}
                </span>
              </div>
            </div>
          </div>
        ))}
        {recentActivity.length === 5 && (
          <div className="text-center pt-2">
            <Button variant="outline" size="sm" onClick={() => setActiveTab('access-requests')}>
              Ver todas las solicitudes
            </Button>
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-12 w-12 animate-spin mx-auto mb-4 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Cargando Dashboard</h2>
          <p className="text-gray-600">Conectando con Supabase cloud via MCP...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <img 
                  src="/lovable-uploads/03a7901c-cc15-4b31-b455-5e45a171f4cf.png" 
                  alt="MSX International" 
                  className="h-8 w-auto object-contain"
                />
                <div className="h-6 w-px bg-gray-300"></div>
                <div className="flex-shrink-0">
                  <div className="p-2 bg-gray-100 rounded-lg">
                    <Shield className="h-5 w-5 text-gray-700" />
                  </div>
                </div>
                <div>
                  <h1 className="text-lg font-semibold text-gray-900">Panel de Administración</h1>
                  <p className="text-sm text-gray-600">MSX International - Valencia HUB</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              {/* User Info */}
              <div className="flex items-center gap-3">
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{user.name}</p>
                                      <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                       {getRoleIcon(user.role)}
                       {user.role === 'superadmin' ? 'Super Admin' : 
                        user.role === 'admin' ? 'Admin' : 'Viewer'}
                      </Badge>
                  </div>
                </div>
                
                <Button onClick={handleLogout} variant="outline" size="sm" className="border-gray-300">
                  <LogOut className="h-4 w-4 mr-2" />
                  Cerrar Sesión
                </Button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-6">
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          <TabsList className="grid w-full bg-gray-100 p-1 rounded-lg" style={{gridTemplateColumns: `repeat(${getAvailableTabs().length}, minmax(0, 1fr))`}}>
            {getAvailableTabs().map((tab) => {
              const Icon = tab.icon;
              return (
                <TabsTrigger
                  key={tab.id}
                  value={tab.id}
                  className={`flex items-center gap-2 px-3 py-2 text-sm font-medium transition-colors rounded-md ${
                    !canAccessTab(tab.id) 
                      ? 'opacity-50 cursor-not-allowed' 
                      : 'data-[state=active]:bg-white data-[state=active]:shadow-sm hover:bg-gray-50'
                  }`}
                  disabled={!canAccessTab(tab.id)}
                >
                  <Icon className="h-4 w-4" />
                  <span className="hidden sm:inline">{tab.label}</span>
                </TabsTrigger>
              );
            })}
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">Resumen del Sistema</h2>
              <div className="flex gap-2">
                <Button onClick={handleRefresh} variant="outline" size="sm">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Actualizar
                </Button>
              </div>
            </div>

            {/* Enhanced Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1, duration: 0.5 }}
                whileHover={{ y: -5 }}
              >
                <Card className="hover:shadow-2xl transition-all duration-500 border-0 bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 relative overflow-hidden group">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <CardContent className="p-8 relative">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <p className="text-sm font-bold text-blue-700 uppercase tracking-wider mb-2">Total Tickets</p>
                        <motion.p 
                          className="text-4xl font-bold text-blue-900"
                          animate={stats.totalTickets > 0 ? { scale: [1, 1.1, 1] } : {}}
                          transition={{ duration: 0.5 }}
                        >
                          {stats.totalTickets}
                        </motion.p>
                      </div>
                      <div className="p-4 bg-gradient-to-br from-blue-500 to-blue-700 rounded-2xl shadow-xl group-hover:shadow-2xl transition-shadow duration-500">
                        <Ticket className="h-8 w-8 text-white" />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="flex items-center gap-2 text-sm bg-white/80 rounded-lg p-2">
                        <div className="w-3 h-3 bg-orange-500 rounded-full animate-pulse shadow-md"></div>
                        <span className="text-blue-800 font-semibold">{stats.openTickets} abiertos</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm bg-white/80 rounded-lg p-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full shadow-md"></div>
                        <span className="text-blue-800 font-semibold">{stats.closedTickets} cerrados</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                whileHover={{ y: -5 }}
              >
                <Card className="hover:shadow-2xl transition-all duration-500 border-0 bg-gradient-to-br from-green-50 via-green-100 to-green-200 relative overflow-hidden group">
                  <div className="absolute inset-0 bg-gradient-to-r from-green-600/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <CardContent className="p-8 relative">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <p className="text-sm font-bold text-green-700 uppercase tracking-wider mb-2">Usuarios Admin</p>
                        <motion.p 
                          className="text-4xl font-bold text-green-900"
                          animate={stats.totalUsers > 0 ? { scale: [1, 1.1, 1] } : {}}
                          transition={{ duration: 0.5, delay: 0.2 }}
                        >
                          {stats.totalUsers}
                        </motion.p>
                      </div>
                      <div className="p-4 bg-gradient-to-br from-green-500 to-green-700 rounded-2xl shadow-xl group-hover:shadow-2xl transition-shadow duration-500">
                        <Users className="h-8 w-8 text-white" />
                      </div>
                    </div>
                    <div className="flex items-center gap-3 bg-white/80 rounded-lg p-3">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <span className="text-sm text-green-800 font-semibold">{stats.activeUsers} usuarios activos</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                whileHover={{ y: -5 }}
              >
                <Card className="hover:shadow-2xl transition-all duration-500 border-0 bg-gradient-to-br from-purple-50 via-purple-100 to-purple-200 relative overflow-hidden group">
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <CardContent className="p-8 relative">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <p className="text-sm font-bold text-purple-700 uppercase tracking-wider mb-2">Inventario</p>
                        <motion.p 
                          className="text-4xl font-bold text-purple-900"
                          animate={stats.totalAssets > 0 ? { scale: [1, 1.1, 1] } : {}}
                          transition={{ duration: 0.5, delay: 0.3 }}
                        >
                          {stats.totalAssets}
                        </motion.p>
                      </div>
                      <div className="p-4 bg-gradient-to-br from-purple-500 to-purple-700 rounded-2xl shadow-xl group-hover:shadow-2xl transition-shadow duration-500">
                        <Package className="h-8 w-8 text-white" />
                      </div>
                    </div>
                    <div className="flex items-center gap-3 bg-white/80 rounded-lg p-3">
                      <Activity className="h-5 w-5 text-purple-600" />
                      <span className="text-sm text-purple-800 font-semibold">Activos gestionados</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                whileHover={{ y: -5 }}
              >
                <Card className="hover:shadow-2xl transition-all duration-500 border-0 bg-gradient-to-br from-orange-50 via-orange-100 to-orange-200 relative overflow-hidden group">
                  <div className="absolute inset-0 bg-gradient-to-r from-orange-600/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <CardContent className="p-8 relative">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <p className="text-sm font-bold text-orange-700 uppercase tracking-wider mb-2">Solicitudes</p>
                        <motion.p 
                          className="text-4xl font-bold text-orange-900"
                          animate={stats.pendingRequests > 0 ? { scale: [1, 1.1, 1] } : {}}
                          transition={{ duration: 0.5, delay: 0.4 }}
                        >
                          {stats.pendingRequests}
                        </motion.p>
                      </div>
                      <div className="p-4 bg-gradient-to-br from-orange-500 to-orange-700 rounded-2xl shadow-xl group-hover:shadow-2xl transition-shadow duration-500">
                        <UserPlus className="h-8 w-8 text-white" />
                      </div>
                    </div>
                    <div className="flex items-center gap-3 bg-white/80 rounded-lg p-3">
                      <Clock className="h-5 w-5 text-orange-600" />
                      <span className="text-sm text-orange-800 font-semibold">{stats.pendingRequests} pendientes</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Recent Activity Section */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Últimas Acciones
                </CardTitle>
              </CardHeader>
              <CardContent>
                                 <RecentActivityList />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Other Tabs */}
          <TabsContent value="tickets" className="space-y-4">
            <SuperiorTicketManager projectId="iexhexcpzjndpgobexnh" />
          </TabsContent>

          <TabsContent value="assets" className="space-y-4">
            {canAccessTab('assets') ? (
              <EnhancedAssetManager projectId="iexhexcpzjndpgobexnh" />
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">No tienes permisos para acceder a esta sección</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="users">
            {canAccessTab('users') ? (
              <SimpleUserManagement />
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">No tienes permisos para acceder a esta sección</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="notifications">
            {canAccessTab('notifications') ? (
              <EnhancedNotifications />
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">No tienes permisos para acceder a esta sección</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="reports">
            {canAccessTab('reports') ? (
              <EnhancedReportsManager projectId="iexhexcpzjndpgobexnh" />
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">No tienes permisos para acceder a esta sección</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="access-requests">
            {canAccessTab('access-requests') ? (
              <AccessRequestManager />
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">No tienes permisos para acceder a esta sección</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
};

export default AdminDashboard;
