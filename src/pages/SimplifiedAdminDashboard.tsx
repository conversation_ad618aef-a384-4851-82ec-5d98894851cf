import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { 
  Shield, 
  LogOut, 
  Ticket, 
  AlertTriangle, 
  Clock,
  CheckCircle,
  MapPin,
  Bell,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Plus,
  Refresh<PERSON>w,
  User,
  Mail,
  Calendar,
  MessageSquare,
  Zap,
  TrendingUp
} from 'lucide-react';

interface TicketData {
  id: string;
  title: string;
  description: string;
  status: string;
  priority: string;
  category: string;
  creator_id: string;
  creator_email: string;
  assigned_to?: string;
  submitter_cdsid?: string;
  submitter_name?: string;
  submitter_market?: string;
  submitter_email?: string;
  affected_cdsid?: string;
  created_at: string;
  updated_at: string;
  closed_at?: string;
  resolution_time?: number;
  tags?: string[];
  related_tickets?: string[];
  ticket_number: number;
  sla_due_date?: string;
  escalated?: boolean;
}

interface TicketStats {
  total: number;
  pending: number;
  in_progress: number;
  resolved: number;
  critical: number;
  high: number;
  medium: number;
  low: number;
}

const SimplifiedAdminDashboard = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [adminUser, setAdminUser] = useState<any>(null);
  const [tickets, setTickets] = useState<TicketData[]>([]);
  const [filteredTickets, setFilteredTickets] = useState<TicketData[]>([]);
  const [stats, setStats] = useState<TicketStats>({
    total: 0, pending: 0, in_progress: 0, resolved: 0,
    critical: 0, high: 0, medium: 0, low: 0
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [selectedTicket, setSelectedTicket] = useState<TicketData | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [newAlerts, setNewAlerts] = useState<string[]>([]);

  // Authentication check
  useEffect(() => {
    const token = localStorage.getItem('adminToken');
    const user = localStorage.getItem('adminUser');
    
    if (!token || token !== 'authenticated') {
      navigate('/');
      return;
    }
    
    if (user) {
      setAdminUser(JSON.parse(user));
    }
  }, [navigate]);

  // Fetch tickets and stats
  const fetchTickets = async () => {
    try {
      setLoading(true);
      
      const { data: ticketsData, error } = await supabase
        .from('tickets')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      setTickets(ticketsData || []);
      
      // Calculate stats
      const ticketStats = (ticketsData || []).reduce((acc, ticket) => {
        acc.total++;
        acc[ticket.status as keyof TicketStats]++;
        acc[ticket.priority as keyof TicketStats]++;
        return acc;
      }, {
        total: 0, pending: 0, in_progress: 0, resolved: 0,
        critical: 0, high: 0, medium: 0, low: 0
      });

      setStats(ticketStats);
    } catch (error) {
      console.error('Error fetching tickets:', error);
      toast({
        title: "Error",
        description: "No se pudieron cargar los tickets",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Real-time subscription
  useEffect(() => {
    fetchTickets();

    console.log(' Estableciendo suscripción en tiempo real...');
    
    const subscription = supabase
      .channel('tickets_realtime_channel')
      .on('postgres_changes', 
        { 
          event: 'INSERT', 
          schema: 'public', 
          table: 'tickets' 
        },
        (payload) => {
          console.log(' Nuevo ticket detectado:', payload);
          
          const newTicket = payload.new as TicketData;
          
          // Agregar a alertas
          setNewAlerts(prev => {
            console.log(' Agregando alerta para ticket:', newTicket.id);
            return [...prev, newTicket.id];
          });
          
          // Mostrar toast
          toast({
            title: " Nuevo Ticket Creado",
            description: `${newTicket.title} - Solicitante: ${newTicket.submitter_cdsid || newTicket.creator_email}`,
            duration: 8000,
          });
          
          // Refrescar datos
          fetchTickets();
        }
      )
      .on('postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'tickets'
        },
        (payload) => {
          console.log(' Ticket actualizado:', payload);
          fetchTickets();
        }
      )
      .subscribe((status) => {
        console.log(' Estado de suscripción:', status);
        if (status === 'SUBSCRIBED') {
          console.log(' Suscripción establecida correctamente');
        }
      });

    return () => {
      console.log(' Desconectando suscripción...');
      subscription.unsubscribe();
    };
  }, [toast]);

  // Real-time subscription for access requests
  useEffect(() => {
    console.log('🔄 Setting up access requests real-time subscription...');

    const accessRequestsChannel = supabase
      .channel('access_requests_admin_channel')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notificaciones_acceso'
        },
        (payload) => {
          console.log('🆕 New access request detected:', payload);

          const newRequest = payload.new;

          // Show toast notification
          toast({
            title: "🆕 Nueva Solicitud de Acceso",
            description: `${newRequest.empleado_nombre} (${newRequest.empleado_cdsid}) - Prioridad: ${newRequest.prioridad}`,
            duration: 8000,
          });
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'notificaciones_acceso'
        },
        (payload) => {
          console.log('🔄 Access request updated:', payload);

          const updatedRequest = payload.new;

          toast({
            title: "🔄 Solicitud de Acceso Actualizada",
            description: `${updatedRequest.empleado_nombre} - Estado: ${updatedRequest.estado}`,
            duration: 6000,
          });
        }
      )
      .subscribe();

    return () => {
      console.log('🔌 Cleaning up access requests subscription...');
      accessRequestsChannel.unsubscribe();
    };
  }, [toast]);

  // Filter tickets
  useEffect(() => {
    let filtered = tickets;

    if (searchTerm) {
      filtered = filtered.filter(ticket =>
        ticket.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ticket.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ticket.submitter_cdsid?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ticket.creator_email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(ticket => ticket.status === statusFilter);
    }

    if (priorityFilter !== 'all') {
      filtered = filtered.filter(ticket => ticket.priority === priorityFilter);
    }

    setFilteredTickets(filtered);
  }, [tickets, searchTerm, statusFilter, priorityFilter]);

  // Update ticket status
  const updateTicketStatus = async (ticketId: string, newStatus: string) => {
    try {
      const { error } = await supabase
        .from('tickets')
        .update({ 
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', ticketId);

      if (error) throw error;

      toast({
        title: "Estado actualizado",
        description: `Ticket actualizado a: ${newStatus}`,
      });

      fetchTickets();
    } catch (error) {
      console.error('Error updating ticket:', error);
      toast({
        title: "Error",
        description: "No se pudo actualizar el ticket",
        variant: "destructive"
      });
    }
  };

  // Delete ticket
  const deleteTicket = async (ticketId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar este ticket?')) return;

    try {
      const { error } = await supabase
        .from('tickets')
        .delete()
        .eq('id', ticketId);

      if (error) throw error;

      toast({
        title: "Ticket eliminado",
        description: "El ticket ha sido eliminado correctamente",
      });

      fetchTickets();
    } catch (error) {
      console.error('Error deleting ticket:', error);
      toast({
        title: "Error",
        description: "No se pudo eliminar el ticket",
        variant: "destructive"
      });
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminUser');
    navigate('/');
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-500 text-white';
      case 'high': return 'bg-orange-500 text-white';
      case 'medium': return 'bg-yellow-500 text-black';
      case 'low': return 'bg-green-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pendiente': return 'bg-yellow-100 text-yellow-800';
      case 'en_progreso': return 'bg-blue-100 text-blue-800';
      case 'resuelto': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pendiente': return Clock;
      case 'en_progreso': return TrendingUp;
      case 'resuelto': return CheckCircle;
      default: return AlertTriangle;
    }
  };

  // Función de prueba para crear ticket
  const createTestTicket = async () => {
    try {
      const testTicket = {
        title: `Ticket de Prueba - ${new Date().toLocaleTimeString()}`,
        description: 'Este es un ticket de prueba para verificar las alertas en tiempo real',
        status: 'pendiente',
        priority: 'medium',
        category: 'test',
        creator_id: adminUser?.id || 'test-admin',
        creator_email: '<EMAIL>',
        submitter_cdsid: 'TEST001',
        submitter_email: '<EMAIL>',
        ticket_number: Math.floor(Math.random() * 10000),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('🧪 Creando ticket de prueba:', testTicket);

      const { data, error } = await supabase
        .from('tickets')
        .insert([testTicket])
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Ticket de prueba creado:', data);
      
      toast({
        title: "🧪 Ticket de Prueba Creado",
        description: "Se ha creado un ticket de prueba para verificar las alertas",
        duration: 3000,
      });

    } catch (error) {
      console.error('❌ Error creando ticket de prueba:', error);
      toast({
        title: "Error",
        description: "No se pudo crear el ticket de prueba",
        variant: "destructive"
      });
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-600 rounded-lg">
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">
                    Dashboard Admin - Valencia HUB
                  </h1>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <MapPin className="h-4 w-4" />
                    <span>Sistema de Gestión de Tickets</span>
                  </div>
                </div>
              </div>
              
              {/* Real-time alerts */}
              <AnimatePresence>
                {newAlerts.length > 0 && (
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0, opacity: 0 }}
                    className="flex items-center gap-2 bg-red-100 text-red-800 px-3 py-2 rounded-lg"
                  >
                    <Bell className="h-4 w-4 animate-pulse" />
                    <span className="font-medium">{newAlerts.length} nuevos tickets</span>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => setNewAlerts([])}
                      className="h-6 w-6 p-0 text-red-600 hover:bg-red-200"
                    >
                      ×
                    </Button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            <div className="flex items-center gap-4">
              <div className="text-right">
                <p className="font-medium text-gray-900">
                  {adminUser?.name || 'Admin User'}
                </p>
                <p className="text-sm text-gray-600">
                  {adminUser?.role || 'Administrador'}
                </p>
              </div>
              <Button
                variant="outline"
                onClick={handleLogout}
                className="flex items-center gap-2"
              >
                <LogOut className="h-4 w-4" />
                Cerrar Sesión
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="p-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100">Total Tickets</p>
                  <p className="text-3xl font-bold">{stats.total}</p>
                </div>
                <Ticket className="h-12 w-12 text-blue-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-yellow-100">Pendientes</p>
                  <p className="text-3xl font-bold">{stats.pending}</p>
                </div>
                <Clock className="h-12 w-12 text-yellow-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100">En Progreso</p>
                  <p className="text-3xl font-bold">{stats.in_progress}</p>
                </div>
                <TrendingUp className="h-12 w-12 text-orange-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100">Resueltos</p>
                  <p className="text-3xl font-bold">{stats.resolved}</p>
                </div>
                <CheckCircle className="h-12 w-12 text-green-200" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-wrap gap-4 items-center">
              <div className="flex-1 min-w-[300px]">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Buscar tickets por título, descripción, CDSID o email..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Estado" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los estados</SelectItem>
                  <SelectItem value="pendiente">Pendiente</SelectItem>
                  <SelectItem value="en_progreso">En Progreso</SelectItem>
                  <SelectItem value="resuelto">Resuelto</SelectItem>
                </SelectContent>
              </Select>

              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Prioridad" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas las prioridades</SelectItem>
                  <SelectItem value="critical">Crítica</SelectItem>
                  <SelectItem value="high">Alta</SelectItem>
                  <SelectItem value="medium">Media</SelectItem>
                  <SelectItem value="low">Baja</SelectItem>
                </SelectContent>
              </Select>

              <Button onClick={fetchTickets} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Actualizar
              </Button>

              <Button onClick={createTestTicket} variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Crear Ticket de Prueba
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Tickets List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Ticket className="h-5 w-5" />
              Tickets Activos ({filteredTickets.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredTickets.length === 0 ? (
                <div className="text-center py-8">
                  <Ticket className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    No hay tickets
                  </h3>
                  <p className="text-gray-600">
                    No se encontraron tickets con los filtros aplicados.
                  </p>
                </div>
              ) : (
                filteredTickets.map((ticket) => {
                  const StatusIcon = getStatusIcon(ticket.status);
                  const isNewAlert = newAlerts.includes(ticket.id);
                  
                  return (
                    <motion.div
                      key={ticket.id}
                      initial={isNewAlert ? { scale: 0.95, opacity: 0 } : false}
                      animate={isNewAlert ? { scale: 1, opacity: 1 } : false}
                      className={`p-4 border rounded-lg hover:shadow-md transition-all ${
                        isNewAlert ? 'border-red-300 bg-red-50' : 'border-gray-200 bg-white'
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="font-semibold text-gray-900">
                              {ticket.title}
                            </h3>
                            <Badge className={getPriorityColor(ticket.priority)}>
                              {ticket.priority}
                            </Badge>
                            <Badge className={getStatusColor(ticket.status)}>
                              <StatusIcon className="h-3 w-3 mr-1" />
                              {ticket.status}
                            </Badge>
                            {isNewAlert && (
                              <Badge className="bg-red-500 text-white animate-pulse">
                                NUEVO
                              </Badge>
                            )}
                          </div>
                          
                          <p className="text-gray-600 mb-3 line-clamp-2">
                            {ticket.description}
                          </p>
                          
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <div className="flex items-center gap-1">
                              <User className="h-4 w-4" />
                              {ticket.submitter_cdsid}
                            </div>
                            <div className="flex items-center gap-1">
                              <Mail className="h-4 w-4" />
                              {ticket.creator_email}
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              {new Date(ticket.created_at).toLocaleDateString('es-ES')}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 ml-4">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setSelectedTicket(ticket)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          
                          <Select
                            value={ticket.status}
                            onValueChange={(value) => updateTicketStatus(ticket.id, value)}
                          >
                            <SelectTrigger className="w-[130px]">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="pendiente">Pendiente</SelectItem>
                              <SelectItem value="en_progreso">En Progreso</SelectItem>
                              <SelectItem value="resuelto">Resuelto</SelectItem>
                            </SelectContent>
                          </Select>
                          
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => deleteTicket(ticket.id)}
                            className="text-red-600 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </motion.div>
                  );
                })
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Ticket Detail Dialog */}
      <Dialog open={!!selectedTicket} onOpenChange={() => setSelectedTicket(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Detalles del Ticket</DialogTitle>
          </DialogHeader>
          {selectedTicket && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Título</label>
                  <p className="text-gray-900">{selectedTicket.title}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Estado</label>
                  <Badge className={getStatusColor(selectedTicket.status)}>
                    {selectedTicket.status}
                  </Badge>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Prioridad</label>
                  <Badge className={getPriorityColor(selectedTicket.priority)}>
                    {selectedTicket.priority}
                  </Badge>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Categoría</label>
                  <p className="text-gray-900">{selectedTicket.category}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">CDSID</label>
                  <p className="text-gray-900">{selectedTicket.submitter_cdsid}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Email</label>
                  <p className="text-gray-900">{selectedTicket.creator_email}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Creado</label>
                  <p className="text-gray-900">
                    {new Date(selectedTicket.created_at).toLocaleString('es-ES')}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Actualizado</label>
                  <p className="text-gray-900">
                    {new Date(selectedTicket.updated_at).toLocaleString('es-ES')}
                  </p>
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-700">Descripción</label>
                <p className="text-gray-900 mt-1 p-3 bg-gray-50 rounded-md">
                  {selectedTicket.description}
                </p>
              </div>
              
              {selectedTicket.affected_cdsid && (
                <div>
                  <label className="text-sm font-medium text-gray-700">CDSID Afectado</label>
                  <p className="text-gray-900">{selectedTicket.affected_cdsid}</p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SimplifiedAdminDashboard;
