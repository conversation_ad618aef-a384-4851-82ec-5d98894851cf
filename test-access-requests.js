// Test script to verify access requests synchronization
// Run this in the browser console to test the functionality

console.log('🧪 Starting Access Requests Synchronization Test...');

// Test 1: Check if Supabase client is working
async function testSupabaseConnection() {
  console.log('🔗 Testing Supabase connection...');
  
  try {
    // Import the supabase client (this would work in the actual app context)
    const response = await fetch('https://iexhexcpzjndpgobexnh.supabase.co/rest/v1/notificaciones_acceso?select=*', {
      headers: {
        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlleGhleGNwempuZHBnb2JleG5oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMjI4MTksImV4cCI6MjA2NTU5ODgxOX0.eH_Ii5SEVkjFyt01hJyyj73Lm2ST5puLLE3CSGxWrHM',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlleGhleGNwempuZHBnb2JleG5oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMjI4MTksImV4cCI6MjA2NTU5ODgxOX0.eH_Ii5SEVkjFyt01hJyyj73Lm2ST5puLLE3CSGxWrHM'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Supabase connection successful. Found', data.length, 'access requests');
      console.log('📋 Access requests:', data);
      return true;
    } else {
      console.error('❌ Supabase connection failed:', response.status, response.statusText);
      return false;
    }
  } catch (error) {
    console.error('❌ Error testing Supabase connection:', error);
    return false;
  }
}

// Test 2: Create a new access request
async function testCreateAccessRequest() {
  console.log('📝 Testing access request creation...');
  
  const testRequest = {
    empleado_nombre: 'Test User ' + Date.now(),
    empleado_cdsid: 'TEST' + Date.now(),
    region: 'Valencia',
    plataformas_faltantes: ['Sistema Test', 'Portal Demo'],
    descripcion: 'Test request created by automated script',
    prioridad: 'media',
    estado: 'pendiente'
  };
  
  try {
    const response = await fetch('https://iexhexcpzjndpgobexnh.supabase.co/rest/v1/notificaciones_acceso', {
      method: 'POST',
      headers: {
        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlleGhleGNwempuZHBnb2JleG5oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMjI4MTksImV4cCI6MjA2NTU5ODgxOX0.eH_Ii5SEVkjFyt01hJyyj73Lm2ST5puLLE3CSGxWrHM',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlleGhleGNwempuZHBnb2JleG5oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMjI4MTksImV4cCI6MjA2NTU5ODgxOX0.eH_Ii5SEVkjFyt01hJyyj73Lm2ST5puLLE3CSGxWrHM',
        'Content-Type': 'application/json',
        'Prefer': 'return=representation'
      },
      body: JSON.stringify(testRequest)
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Access request created successfully:', data);
      return data[0];
    } else {
      const errorText = await response.text();
      console.error('❌ Failed to create access request:', response.status, errorText);
      return null;
    }
  } catch (error) {
    console.error('❌ Error creating access request:', error);
    return null;
  }
}

// Test 3: Check if the request appears in the UI
function testUIUpdate() {
  console.log('🔍 Checking if access requests appear in the UI...');
  
  // Look for notification elements in the DOM
  const notificationElements = document.querySelectorAll('[data-testid="notification"], .notification-item, .access-request-item');
  console.log('📋 Found', notificationElements.length, 'notification elements in DOM');
  
  // Look for specific text that indicates access requests
  const textElements = document.querySelectorAll('*');
  let foundAccessRequests = 0;
  
  textElements.forEach(el => {
    if (el.textContent && (
      el.textContent.includes('Juan Pérez') ||
      el.textContent.includes('María García') ||
      el.textContent.includes('Carlos López') ||
      el.textContent.includes('solicitud') ||
      el.textContent.includes('acceso')
    )) {
      foundAccessRequests++;
    }
  });
  
  console.log('🔍 Found', foundAccessRequests, 'elements containing access request related text');
  
  // Check for specific components
  const dashboardTabs = document.querySelector('[role="tablist"]');
  if (dashboardTabs) {
    console.log('✅ Found dashboard tabs component');
  } else {
    console.log('❌ Dashboard tabs component not found');
  }
  
  return foundAccessRequests > 0;
}

// Test 4: Check localStorage fallback
function testLocalStorageFallback() {
  console.log('💾 Testing localStorage fallback...');
  
  const localRequests = localStorage.getItem('ford_access_requests');
  if (localRequests) {
    const requests = JSON.parse(localRequests);
    console.log('✅ Found', requests.length, 'requests in localStorage:', requests);
    return requests;
  } else {
    console.log('ℹ️ No requests found in localStorage');
    return [];
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Running comprehensive access requests test suite...');
  console.log('='.repeat(60));
  
  const results = {
    supabaseConnection: false,
    createRequest: null,
    uiUpdate: false,
    localStorage: []
  };
  
  // Test 1: Supabase connection
  results.supabaseConnection = await testSupabaseConnection();
  console.log('');
  
  // Test 2: Create request
  results.createRequest = await testCreateAccessRequest();
  console.log('');
  
  // Test 3: UI update
  results.uiUpdate = testUIUpdate();
  console.log('');
  
  // Test 4: localStorage
  results.localStorage = testLocalStorageFallback();
  console.log('');
  
  // Summary
  console.log('📊 TEST RESULTS SUMMARY:');
  console.log('='.repeat(60));
  console.log('🔗 Supabase Connection:', results.supabaseConnection ? '✅ PASS' : '❌ FAIL');
  console.log('📝 Create Request:', results.createRequest ? '✅ PASS' : '❌ FAIL');
  console.log('🖥️ UI Update:', results.uiUpdate ? '✅ PASS' : '❌ FAIL');
  console.log('💾 localStorage:', results.localStorage.length > 0 ? '✅ HAS DATA' : 'ℹ️ EMPTY');
  
  const passedTests = [
    results.supabaseConnection,
    !!results.createRequest,
    results.uiUpdate
  ].filter(Boolean).length;
  
  console.log('');
  console.log('🎯 OVERALL RESULT:', passedTests >= 2 ? '✅ SYSTEM WORKING' : '❌ NEEDS ATTENTION');
  console.log(`📈 Score: ${passedTests}/3 tests passed`);
  
  return results;
}

// Auto-run tests when script is loaded
if (typeof window !== 'undefined') {
  // Wait a bit for the page to load
  setTimeout(() => {
    runAllTests().then(results => {
      console.log('🏁 Test suite completed!');
      
      // Store results globally for manual inspection
      window.testResults = results;
      console.log('💡 Results stored in window.testResults for inspection');
    });
  }, 2000);
} else {
  console.log('ℹ️ Script loaded but not in browser environment');
}

// Export functions for manual testing
if (typeof window !== 'undefined') {
  window.accessRequestTests = {
    testSupabaseConnection,
    testCreateAccessRequest,
    testUIUpdate,
    testLocalStorageFallback,
    runAllTests
  };
  
  console.log('🛠️ Test functions available in window.accessRequestTests');
  console.log('📖 Usage: window.accessRequestTests.runAllTests()');
}
