<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin Dashboard - Portal MSX</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .stat-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .stat-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .expected {
            color: #28a745;
            font-weight: bold;
        }
        .actual {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test Admin Dashboard - Portal MSX</h1>
        <p>Esta página verifica que el admin dashboard muestre las estadísticas correctas.</p>
        
        <div class="info">
            <h3>📊 Estado Esperado del Sistema</h3>
            <div id="expected-stats">
                <p><strong>Solicitudes de Acceso:</strong> <span class="expected">13 total (12 pendientes)</span></p>
                <p><strong>Tickets:</strong> <span class="expected">3 total (2 abiertos)</span></p>
                <p><strong>Usuarios Admin:</strong> <span class="expected">1+ activos</span></p>
                <p><strong>Inventario:</strong> <span class="expected">0 (no configurado)</span></p>
            </div>
        </div>
    </div>

    <div class="grid">
        <!-- Panel de Tests -->
        <div class="container">
            <h2>🧪 Tests del Admin Dashboard</h2>
            
            <div style="margin-bottom: 20px;">
                <h4>🔗 Conectividad</h4>
                <button onclick="testAdminDashboardAccess()">Test Acceso Dashboard</button>
                <button onclick="testSupabaseData()">Test Datos Supabase</button>
                <div id="connectivity-results"></div>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h4>📊 Estadísticas</h4>
                <button onclick="verifyStats()">Verificar Estadísticas</button>
                <button onclick="createTestData()">Crear Datos de Prueba</button>
                <button onclick="testRealTimeUpdates()">Test Tiempo Real</button>
                <div id="stats-results"></div>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h4>🚀 Test Completo</h4>
                <button onclick="runCompleteAdminTest()">Ejecutar Test Completo</button>
                <button onclick="openAdminDashboard()">Abrir Admin Dashboard</button>
                <div id="complete-results"></div>
            </div>
        </div>

        <!-- Panel de Resultados -->
        <div class="container">
            <h2>📈 Estadísticas Actuales</h2>
            <div id="current-stats"></div>
            
            <h3>📝 Log de Actividad</h3>
            <div id="activity-log" class="log"></div>
            <button onclick="clearLog()">Limpiar Log</button>
        </div>
    </div>

    <script>
        // Configuración
        const SUPABASE_URL = "https://iexhexcpzjndpgobexnh.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlleGhleGNwempuZHBnb2JleG5oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMjI4MTksImV4cCI6MjA2NTU5ODgxOX0.eH_Ii5SEVkjFyt01hJyyj73Lm2ST5puLLE3CSGxWrHM";
        const ADMIN_DASHBOARD_URL = "http://localhost:8081/admin-dashboard";

        let currentStats = {};

        // Funciones de utilidad
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            
            const logElement = document.getElementById('activity-log');
            logElement.textContent += logEntry + '\n';
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }

        function clearLog() {
            document.getElementById('activity-log').textContent = '';
        }

        function updateCurrentStats(stats) {
            currentStats = stats;
            const statsElement = document.getElementById('current-stats');
            statsElement.innerHTML = `
                <div class="stat-card">
                    <h4>📝 Solicitudes de Acceso</h4>
                    <div class="stat-number">${stats.totalRequests || 0}</div>
                    <p>Pendientes: ${stats.pendingRequests || 0}</p>
                    <p>Aprobadas: ${stats.approvedRequests || 0}</p>
                    <p>Rechazadas: ${stats.rejectedRequests || 0}</p>
                </div>
                <div class="stat-card">
                    <h4>🎫 Tickets</h4>
                    <div class="stat-number">${stats.totalTickets || 0}</div>
                    <p>Abiertos: ${stats.openTickets || 0}</p>
                    <p>Cerrados: ${stats.closedTickets || 0}</p>
                </div>
                <div class="stat-card">
                    <h4>👥 Usuarios</h4>
                    <div class="stat-number">${stats.totalUsers || 0}</div>
                    <p>Activos: ${stats.activeUsers || 0}</p>
                </div>
                <div class="stat-card">
                    <h4>📦 Inventario</h4>
                    <div class="stat-number">${stats.totalAssets || 0}</div>
                    <p>Activos gestionados</p>
                </div>
            `;
        }

        // Tests específicos
        async function testAdminDashboardAccess() {
            log('🔗 Testing admin dashboard access...');
            
            try {
                const response = await fetch(ADMIN_DASHBOARD_URL);
                if (response.ok) {
                    log('✅ Admin dashboard accessible');
                    document.getElementById('connectivity-results').innerHTML = '<div class="success">✅ Admin dashboard accesible</div>';
                    return true;
                } else {
                    log(`❌ Admin dashboard error: ${response.status}`);
                    document.getElementById('connectivity-results').innerHTML = '<div class="error">❌ Error de acceso al dashboard</div>';
                    return false;
                }
            } catch (error) {
                log(`❌ Admin dashboard error: ${error.message}`);
                document.getElementById('connectivity-results').innerHTML = '<div class="error">❌ Error de conexión</div>';
                return false;
            }
        }

        async function testSupabaseData() {
            log('📊 Testing Supabase data access...');
            
            try {
                // Test access requests
                const requestsResponse = await fetch(`${SUPABASE_URL}/rest/v1/notificaciones_acceso?select=*`, {
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                    }
                });

                // Test tickets
                const ticketsResponse = await fetch(`${SUPABASE_URL}/rest/v1/tickets?select=*`, {
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                    }
                });

                if (requestsResponse.ok && ticketsResponse.ok) {
                    const requests = await requestsResponse.json();
                    const tickets = await ticketsResponse.json();

                    const stats = {
                        totalRequests: requests.length,
                        pendingRequests: requests.filter(r => r.estado === 'pendiente').length,
                        approvedRequests: requests.filter(r => r.estado === 'aprobada').length,
                        rejectedRequests: requests.filter(r => r.estado === 'rechazada').length,
                        totalTickets: tickets.length,
                        openTickets: tickets.filter(t => t.status === 'open').length,
                        closedTickets: tickets.filter(t => t.status === 'closed').length,
                        totalUsers: 1, // Mock data
                        activeUsers: 1, // Mock data
                        totalAssets: 0 // No assets configured
                    };

                    updateCurrentStats(stats);
                    log(`✅ Data loaded: ${stats.totalRequests} requests, ${stats.totalTickets} tickets`);
                    return stats;
                } else {
                    log('❌ Failed to load Supabase data');
                    return null;
                }
            } catch (error) {
                log(`❌ Supabase data error: ${error.message}`);
                return null;
            }
        }

        async function verifyStats() {
            log('🔍 Verifying admin dashboard statistics...');
            
            const stats = await testSupabaseData();
            if (!stats) {
                document.getElementById('stats-results').innerHTML = '<div class="error">❌ No se pudieron obtener las estadísticas</div>';
                return false;
            }

            // Expected values
            const expected = {
                totalRequests: 13,
                pendingRequests: 12,
                totalTickets: 3,
                openTickets: 2
            };

            let allCorrect = true;
            let results = '<div class="info"><h4>📊 Verificación de Estadísticas</h4>';

            // Check requests
            if (stats.totalRequests === expected.totalRequests) {
                results += `<p>✅ Solicitudes totales: <span class="expected">${stats.totalRequests}</span> (correcto)</p>`;
            } else {
                results += `<p>❌ Solicitudes totales: <span class="actual">${stats.totalRequests}</span> (esperado: <span class="expected">${expected.totalRequests}</span>)</p>`;
                allCorrect = false;
            }

            if (stats.pendingRequests === expected.pendingRequests) {
                results += `<p>✅ Solicitudes pendientes: <span class="expected">${stats.pendingRequests}</span> (correcto)</p>`;
            } else {
                results += `<p>❌ Solicitudes pendientes: <span class="actual">${stats.pendingRequests}</span> (esperado: <span class="expected">${expected.pendingRequests}</span>)</p>`;
                allCorrect = false;
            }

            // Check tickets
            if (stats.totalTickets === expected.totalTickets) {
                results += `<p>✅ Tickets totales: <span class="expected">${stats.totalTickets}</span> (correcto)</p>`;
            } else {
                results += `<p>❌ Tickets totales: <span class="actual">${stats.totalTickets}</span> (esperado: <span class="expected">${expected.totalTickets}</span>)</p>`;
                allCorrect = false;
            }

            if (stats.openTickets === expected.openTickets) {
                results += `<p>✅ Tickets abiertos: <span class="expected">${stats.openTickets}</span> (correcto)</p>`;
            } else {
                results += `<p>❌ Tickets abiertos: <span class="actual">${stats.openTickets}</span> (esperado: <span class="expected">${expected.openTickets}</span>)</p>`;
                allCorrect = false;
            }

            results += '</div>';

            if (allCorrect) {
                results = '<div class="success">🎉 Todas las estadísticas son correctas!</div>' + results;
                log('🎉 All statistics are correct!');
            } else {
                results = '<div class="error">⚠️ Algunas estadísticas no coinciden</div>' + results;
                log('⚠️ Some statistics do not match expected values');
            }

            document.getElementById('stats-results').innerHTML = results;
            return allCorrect;
        }

        async function createTestData() {
            log('📝 Creating test data...');
            
            const testRequest = {
                empleado_nombre: `Admin Test ${Date.now()}`,
                empleado_cdsid: `ADM${Date.now()}`,
                region: 'Test Admin',
                plataformas_faltantes: ['Sistema Admin Test', 'Portal Test'],
                descripcion: 'Test de creación desde verificación de admin dashboard',
                prioridad: 'alta',
                estado: 'pendiente'
            };

            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/notificaciones_acceso`, {
                    method: 'POST',
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json',
                        'Prefer': 'return=representation'
                    },
                    body: JSON.stringify(testRequest)
                });

                if (response.ok) {
                    log('✅ Test data created successfully');
                    // Wait a moment and then verify stats again
                    setTimeout(verifyStats, 2000);
                    return true;
                } else {
                    log(`❌ Failed to create test data: ${response.status}`);
                    return false;
                }
            } catch (error) {
                log(`❌ Create test data error: ${error.message}`);
                return false;
            }
        }

        async function testRealTimeUpdates() {
            log('📡 Testing real-time updates...');
            
            // Create test data and monitor for updates
            await createTestData();
            
            log('✅ Real-time test initiated - check admin dashboard for immediate updates');
            document.getElementById('stats-results').innerHTML += '<div class="info">📡 Test de tiempo real iniciado - verifica el admin dashboard</div>';
        }

        function openAdminDashboard() {
            log('🌐 Opening admin dashboard...');
            window.open(ADMIN_DASHBOARD_URL, '_blank');
        }

        async function runCompleteAdminTest() {
            log('🚀 Running complete admin dashboard test...');
            
            // Test access
            const accessOk = await testAdminDashboardAccess();
            
            // Test data
            const stats = await testSupabaseData();
            
            // Verify stats
            const statsOk = await verifyStats();
            
            // Test real-time
            await testRealTimeUpdates();
            
            // Open dashboard for manual verification
            openAdminDashboard();
            
            const summary = `
                <div class="${accessOk && stats && statsOk ? 'success' : 'error'}">
                    <h4>📊 Resumen del Test Completo</h4>
                    <p>✅ Acceso al dashboard: ${accessOk ? 'OK' : 'Error'}</p>
                    <p>✅ Datos de Supabase: ${stats ? 'OK' : 'Error'}</p>
                    <p>✅ Estadísticas correctas: ${statsOk ? 'OK' : 'Verificar'}</p>
                    <p>📡 Tiempo real: Iniciado</p>
                    <p>🌐 Dashboard abierto para verificación manual</p>
                </div>
            `;
            
            document.getElementById('complete-results').innerHTML = summary;
            
            if (accessOk && stats && statsOk) {
                log('🎉 Complete admin dashboard test PASSED!');
            } else {
                log('⚠️ Some tests failed - check results above');
            }
        }

        // Inicialización
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Admin dashboard test page loaded');
            
            // Auto-run basic tests
            setTimeout(async () => {
                await testSupabaseData();
            }, 1000);
        });
    </script>
</body>
</html>
