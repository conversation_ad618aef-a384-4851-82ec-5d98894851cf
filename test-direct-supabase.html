<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Supabase Directo - Portal MSX</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Test Supabase Directo - Portal MSX</h1>
        <p>Esta página verifica que el portal funcione correctamente usando Supabase directamente (sin MCP).</p>
        
        <div class="info">
            <h3>📊 Estado del Sistema</h3>
            <div id="system-status">
                <p>Verificando conexión directa a Supabase...</p>
            </div>
        </div>
    </div>

    <div class="grid">
        <!-- Panel de Tests -->
        <div class="container">
            <h2>🧪 Tests Directos</h2>
            
            <div style="margin-bottom: 20px;">
                <h4>🔗 Conectividad Supabase</h4>
                <button onclick="testSupabaseConnection()">Test Conexión</button>
                <button onclick="testSupabaseAuth()">Test Auth</button>
                <div id="connectivity-results"></div>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h4>📋 Datos de Solicitudes</h4>
                <button onclick="testFetchRequests()">Obtener Solicitudes</button>
                <button onclick="testCreateRequest()">Crear Solicitud</button>
                <button onclick="testUpdateRequest()">Actualizar Estado</button>
                <div id="data-results"></div>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h4>🎫 Datos de Tickets</h4>
                <button onclick="testFetchTickets()">Obtener Tickets</button>
                <button onclick="testCreateTicket()">Crear Ticket</button>
                <div id="tickets-results"></div>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h4>🚀 Test Completo</h4>
                <button onclick="runCompleteTest()">Ejecutar Todos los Tests</button>
                <div id="complete-results"></div>
            </div>
        </div>

        <!-- Panel de Resultados -->
        <div class="container">
            <h2>📈 Resultados</h2>
            <div id="test-results"></div>
            
            <h3>📝 Log de Actividad</h3>
            <div id="activity-log" class="log"></div>
            <button onclick="clearLog()">Limpiar Log</button>
        </div>
    </div>

    <script>
        // Configuración Supabase
        const SUPABASE_URL = "https://iexhexcpzjndpgobexnh.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlleGhleGNwempuZHBnb2JleG5oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMjI4MTksImV4cCI6MjA2NTU5ODgxOX0.eH_Ii5SEVkjFyt01hJyyj73Lm2ST5puLLE3CSGxWrHM";

        let testResults = [];

        // Funciones de utilidad
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            
            const logElement = document.getElementById('activity-log');
            logElement.textContent += logEntry + '\n';
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }

        function addResult(test, success, message) {
            testResults.push({ test, success, message, timestamp: new Date() });
            updateResults();
        }

        function updateResults() {
            const resultsElement = document.getElementById('test-results');
            resultsElement.innerHTML = testResults.map(result => `
                <div class="${result.success ? 'success' : 'error'}">
                    <strong>${result.test}</strong>: ${result.message}
                    <small>(${result.timestamp.toLocaleTimeString()})</small>
                </div>
            `).join('');
        }

        function clearLog() {
            document.getElementById('activity-log').textContent = '';
        }

        // Tests de conectividad
        async function testSupabaseConnection() {
            log('🔗 Testing direct Supabase connection...');
            
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/`, {
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                    }
                });
                
                if (response.ok) {
                    addResult('Conexión Supabase', true, 'Conexión directa exitosa');
                    log('✅ Direct Supabase connection successful');
                    document.getElementById('connectivity-results').innerHTML = '<div class="success">✅ Supabase conectado directamente</div>';
                    return true;
                } else {
                    addResult('Conexión Supabase', false, `Error ${response.status}`);
                    log(`❌ Supabase connection failed: ${response.status}`);
                    return false;
                }
            } catch (error) {
                addResult('Conexión Supabase', false, error.message);
                log(`❌ Supabase error: ${error.message}`);
                return false;
            }
        }

        async function testSupabaseAuth() {
            log('🔐 Testing Supabase auth...');
            
            try {
                const response = await fetch(`${SUPABASE_URL}/auth/v1/user`, {
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                    }
                });
                
                addResult('Auth Supabase', true, 'Auth endpoint accesible');
                log('✅ Supabase auth endpoint accessible');
                return true;
            } catch (error) {
                addResult('Auth Supabase', false, error.message);
                log(`❌ Supabase auth error: ${error.message}`);
                return false;
            }
        }

        // Tests de datos
        async function testFetchRequests() {
            log('📋 Testing fetch access requests...');
            
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/notificaciones_acceso?select=*&order=created_at.desc`, {
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult('Obtener Solicitudes', true, `${data.length} solicitudes obtenidas`);
                    log(`✅ Fetched ${data.length} access requests directly from Supabase`);
                    
                    document.getElementById('data-results').innerHTML = `
                        <div class="success">✅ ${data.length} solicitudes obtenidas directamente</div>
                    `;
                    
                    return data;
                } else {
                    addResult('Obtener Solicitudes', false, `Error ${response.status}`);
                    log(`❌ Failed to fetch requests: ${response.status}`);
                    return [];
                }
            } catch (error) {
                addResult('Obtener Solicitudes', false, error.message);
                log(`❌ Fetch requests error: ${error.message}`);
                return [];
            }
        }

        async function testCreateRequest() {
            log('📝 Testing create access request...');
            
            const testData = {
                empleado_nombre: `Test Direct ${Date.now()}`,
                empleado_cdsid: `TD${Date.now()}`,
                region: 'Test Region',
                plataformas_faltantes: ['Sistema Test', 'Portal Test'],
                descripcion: 'Test de creación directa en Supabase',
                prioridad: 'media',
                estado: 'pendiente'
            };
            
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/notificaciones_acceso`, {
                    method: 'POST',
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json',
                        'Prefer': 'return=representation'
                    },
                    body: JSON.stringify(testData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    addResult('Crear Solicitud', true, 'Solicitud creada directamente');
                    log('✅ Access request created directly in Supabase');
                    return result[0];
                } else {
                    addResult('Crear Solicitud', false, `Error ${response.status}`);
                    log(`❌ Failed to create request: ${response.status}`);
                    return null;
                }
            } catch (error) {
                addResult('Crear Solicitud', false, error.message);
                log(`❌ Create request error: ${error.message}`);
                return null;
            }
        }

        async function testUpdateRequest() {
            log('🔄 Testing update request status...');
            
            // First get a request to update
            const requests = await testFetchRequests();
            if (requests.length === 0) {
                addResult('Actualizar Solicitud', false, 'No hay solicitudes para actualizar');
                return false;
            }
            
            const requestToUpdate = requests[0];
            const newStatus = requestToUpdate.estado === 'pendiente' ? 'aprobada' : 'pendiente';
            
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/notificaciones_acceso?id=eq.${requestToUpdate.id}`, {
                    method: 'PATCH',
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        estado: newStatus,
                        updated_at: new Date().toISOString()
                    })
                });
                
                if (response.ok) {
                    addResult('Actualizar Solicitud', true, `Estado cambiado a ${newStatus}`);
                    log(`✅ Request status updated to ${newStatus}`);
                    return true;
                } else {
                    addResult('Actualizar Solicitud', false, `Error ${response.status}`);
                    log(`❌ Failed to update request: ${response.status}`);
                    return false;
                }
            } catch (error) {
                addResult('Actualizar Solicitud', false, error.message);
                log(`❌ Update request error: ${error.message}`);
                return false;
            }
        }

        async function testFetchTickets() {
            log('🎫 Testing fetch tickets...');
            
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/tickets?select=*&order=created_at.desc`, {
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult('Obtener Tickets', true, `${data.length} tickets obtenidos`);
                    log(`✅ Fetched ${data.length} tickets directly from Supabase`);
                    
                    document.getElementById('tickets-results').innerHTML = `
                        <div class="success">✅ ${data.length} tickets obtenidos directamente</div>
                    `;
                    
                    return data;
                } else {
                    addResult('Obtener Tickets', false, `Error ${response.status}`);
                    log(`❌ Failed to fetch tickets: ${response.status}`);
                    return [];
                }
            } catch (error) {
                addResult('Obtener Tickets', false, error.message);
                log(`❌ Fetch tickets error: ${error.message}`);
                return [];
            }
        }

        async function testCreateTicket() {
            log('🎫 Testing create ticket...');
            
            const testData = {
                title: `Test Ticket ${Date.now()}`,
                description: 'Test de creación directa de ticket en Supabase',
                category: 'test',
                priority: 'medium',
                status: 'open',
                creator_email: '<EMAIL>',
                creator_id: 'test-user'
            };
            
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/tickets`, {
                    method: 'POST',
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json',
                        'Prefer': 'return=representation'
                    },
                    body: JSON.stringify(testData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    addResult('Crear Ticket', true, 'Ticket creado directamente');
                    log('✅ Ticket created directly in Supabase');
                    return result[0];
                } else {
                    addResult('Crear Ticket', false, `Error ${response.status}`);
                    log(`❌ Failed to create ticket: ${response.status}`);
                    return null;
                }
            } catch (error) {
                addResult('Crear Ticket', false, error.message);
                log(`❌ Create ticket error: ${error.message}`);
                return null;
            }
        }

        // Test completo
        async function runCompleteTest() {
            log('🚀 Running complete direct Supabase test...');
            testResults = []; // Clear previous results
            
            // Tests de conectividad
            await testSupabaseConnection();
            await testSupabaseAuth();
            
            // Tests de datos
            const requests = await testFetchRequests();
            await testCreateRequest();
            await testUpdateRequest();
            
            // Tests de tickets
            const tickets = await testFetchTickets();
            await testCreateTicket();
            
            // Resumen final
            const successCount = testResults.filter(r => r.success).length;
            const totalCount = testResults.length;
            
            log(`🎯 Complete test finished: ${successCount}/${totalCount} tests passed`);
            
            if (successCount === totalCount) {
                log('🎉 ALL TESTS PASSED! Direct Supabase integration working perfectly.');
                document.getElementById('system-status').innerHTML = `
                    <p><span class="status-indicator status-success"></span>Supabase directo completamente funcional</p>
                    <p><span class="status-indicator status-success"></span>Solicitudes: ${requests.length} encontradas</p>
                    <p><span class="status-indicator status-success"></span>Tickets: ${tickets.length} encontrados</p>
                    <p><span class="status-indicator status-success"></span>Tests: ${successCount}/${totalCount} exitosos</p>
                `;
            } else {
                log('⚠️ Some tests failed. Please check the results.');
                document.getElementById('system-status').innerHTML = `
                    <p><span class="status-indicator status-warning"></span>Sistema parcialmente funcional</p>
                    <p><span class="status-indicator status-warning"></span>Tests: ${successCount}/${totalCount} exitosos</p>
                `;
            }
        }

        // Inicialización
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Direct Supabase test page loaded');
            
            // Auto-run basic connectivity tests
            setTimeout(async () => {
                await testSupabaseConnection();
                await testFetchRequests();
            }, 1000);
        });
    </script>
</body>
</html>
