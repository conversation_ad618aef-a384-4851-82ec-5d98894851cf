<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Notificaciones - Portal MSX</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .notification-item {
            border: 1px solid #ddd;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug de Notificaciones Portal MSX</h1>
        <p>Esta página ayuda a diagnosticar problemas con las notificaciones de acceso.</p>
        
        <div class="test-section info">
            <h3>📊 Estado del Sistema</h3>
            <div id="system-status">
                <p>Verificando estado del sistema...</p>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🧪 Tests de Diagnóstico</h2>
        
        <div class="test-section">
            <h4>🔗 Conexión a Supabase</h4>
            <button onclick="testSupabaseConnection()">Test Conexión</button>
            <button onclick="testSupabaseData()">Test Datos</button>
            <button onclick="createTestNotification()">Crear Notificación Test</button>
            <div id="supabase-results"></div>
        </div>
        
        <div class="test-section">
            <h4>🌐 Test del Portal</h4>
            <button onclick="testPortalConnection()">Test Portal</button>
            <button onclick="inspectPortalState()">Inspeccionar Estado</button>
            <button onclick="triggerPortalRefresh()">Forzar Actualización</button>
            <div id="portal-results"></div>
        </div>
        
        <div class="test-section">
            <h4>💾 LocalStorage</h4>
            <button onclick="checkLocalStorage()">Verificar LocalStorage</button>
            <button onclick="clearLocalStorage()">Limpiar LocalStorage</button>
            <button onclick="addTestToLocalStorage()">Agregar Test Local</button>
            <div id="localstorage-results"></div>
        </div>
    </div>

    <div class="container">
        <h2>📋 Resultados de Notificaciones</h2>
        <div id="notifications-display"></div>
    </div>

    <div class="container">
        <h2>📝 Log de Actividad</h2>
        <div id="activity-log" class="log"></div>
        <button onclick="clearLog()">Limpiar Log</button>
    </div>

    <script>
        // Configuration
        const SUPABASE_URL = "https://iexhexcpzjndpgobexnh.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlleGhleGNwempuZHBnb2JleG5oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMjI4MTksImV4cCI6MjA2NTU5ODgxOX0.eH_Ii5SEVkjFyt01hJyyj73Lm2ST5puLLE3CSGxWrHM";
        const PORTAL_URL = "http://localhost:8081";

        // Logging function
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            
            const logElement = document.getElementById('activity-log');
            logElement.textContent += logEntry + '\n';
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }

        function clearLog() {
            document.getElementById('activity-log').textContent = '';
        }

        // Supabase tests
        async function testSupabaseConnection() {
            log('🔗 Testing Supabase connection...');
            
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/`, {
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                    }
                });
                
                if (response.ok) {
                    log('✅ Supabase connection successful');
                    document.getElementById('supabase-results').innerHTML = '<div class="success">✅ Conexión exitosa</div>';
                    return true;
                } else {
                    log(`❌ Supabase connection failed: ${response.status}`);
                    document.getElementById('supabase-results').innerHTML = `<div class="error">❌ Error: ${response.status}</div>`;
                    return false;
                }
            } catch (error) {
                log(`❌ Supabase connection error: ${error.message}`);
                document.getElementById('supabase-results').innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                return false;
            }
        }

        async function testSupabaseData() {
            log('📊 Testing Supabase data retrieval...');
            
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/notificaciones_acceso?select=*`, {
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Retrieved ${data.length} notifications from Supabase`);
                    
                    document.getElementById('supabase-results').innerHTML = `
                        <div class="success">✅ Encontradas ${data.length} notificaciones</div>
                    `;
                    
                    displayNotifications(data);
                    return data;
                } else {
                    log(`❌ Failed to retrieve data: ${response.status}`);
                    document.getElementById('supabase-results').innerHTML = `<div class="error">❌ Error al obtener datos: ${response.status}</div>`;
                    return [];
                }
            } catch (error) {
                log(`❌ Data retrieval error: ${error.message}`);
                document.getElementById('supabase-results').innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                return [];
            }
        }

        async function createTestNotification() {
            log('📝 Creating test notification...');
            
            const testData = {
                empleado_nombre: `Test User ${Date.now()}`,
                empleado_cdsid: `TEST${Date.now()}`,
                region: 'Valencia',
                plataformas_faltantes: ['Sistema Test', 'Portal Debug'],
                descripcion: 'Notificación de prueba creada desde debug',
                prioridad: 'media',
                estado: 'pendiente'
            };
            
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/notificaciones_acceso`, {
                    method: 'POST',
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json',
                        'Prefer': 'return=representation'
                    },
                    body: JSON.stringify(testData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    log('✅ Test notification created successfully');
                    document.getElementById('supabase-results').innerHTML = '<div class="success">✅ Notificación test creada</div>';
                    
                    // Refresh data display
                    setTimeout(() => testSupabaseData(), 1000);
                    
                    return result;
                } else {
                    const errorText = await response.text();
                    log(`❌ Failed to create test notification: ${response.status} - ${errorText}`);
                    document.getElementById('supabase-results').innerHTML = `<div class="error">❌ Error al crear: ${response.status}</div>`;
                    return null;
                }
            } catch (error) {
                log(`❌ Create notification error: ${error.message}`);
                document.getElementById('supabase-results').innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                return null;
            }
        }

        // Portal tests
        async function testPortalConnection() {
            log('🌐 Testing portal connection...');
            
            try {
                const response = await fetch(PORTAL_URL);
                if (response.ok) {
                    log('✅ Portal is accessible');
                    document.getElementById('portal-results').innerHTML = '<div class="success">✅ Portal accesible</div>';
                    return true;
                } else {
                    log(`❌ Portal connection failed: ${response.status}`);
                    document.getElementById('portal-results').innerHTML = `<div class="error">❌ Error: ${response.status}</div>`;
                    return false;
                }
            } catch (error) {
                log(`❌ Portal connection error: ${error.message}`);
                document.getElementById('portal-results').innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                return false;
            }
        }

        function inspectPortalState() {
            log('🔍 Inspecting portal state...');
            
            // This would work if run in the portal's context
            const info = {
                url: window.location.href,
                localStorage: Object.keys(localStorage).length,
                sessionStorage: Object.keys(sessionStorage).length,
                userAgent: navigator.userAgent.substring(0, 50) + '...'
            };
            
            log(`Portal info: ${JSON.stringify(info, null, 2)}`);
            document.getElementById('portal-results').innerHTML = `
                <div class="info">
                    <strong>Estado del Portal:</strong><br>
                    URL: ${info.url}<br>
                    LocalStorage items: ${info.localStorage}<br>
                    SessionStorage items: ${info.sessionStorage}
                </div>
            `;
        }

        function triggerPortalRefresh() {
            log('🔄 Triggering portal refresh...');
            
            // Open portal in new tab
            window.open(PORTAL_URL, '_blank');
            
            document.getElementById('portal-results').innerHTML = '<div class="info">🔄 Portal abierto en nueva pestaña</div>';
        }

        // LocalStorage tests
        function checkLocalStorage() {
            log('💾 Checking localStorage...');
            
            const fordRequests = localStorage.getItem('ford_access_requests');
            
            if (fordRequests) {
                try {
                    const requests = JSON.parse(fordRequests);
                    log(`✅ Found ${requests.length} requests in localStorage`);
                    document.getElementById('localstorage-results').innerHTML = `
                        <div class="success">✅ ${requests.length} solicitudes en localStorage</div>
                    `;
                    displayNotifications(requests);
                } catch (error) {
                    log(`❌ Error parsing localStorage: ${error.message}`);
                    document.getElementById('localstorage-results').innerHTML = `<div class="error">❌ Error parsing: ${error.message}</div>`;
                }
            } else {
                log('ℹ️ No requests found in localStorage');
                document.getElementById('localstorage-results').innerHTML = '<div class="info">ℹ️ No hay datos en localStorage</div>';
            }
        }

        function clearLocalStorage() {
            localStorage.removeItem('ford_access_requests');
            log('🧹 LocalStorage cleared');
            document.getElementById('localstorage-results').innerHTML = '<div class="info">🧹 LocalStorage limpiado</div>';
        }

        function addTestToLocalStorage() {
            const testData = {
                id: `local-${Date.now()}`,
                empleado_nombre: `Local Test ${Date.now()}`,
                empleado_cdsid: `LOCAL${Date.now()}`,
                region: 'Valencia',
                plataformas_faltantes: ['Test Local'],
                descripcion: 'Test desde localStorage',
                prioridad: 'baja',
                estado: 'pendiente',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };
            
            const existing = JSON.parse(localStorage.getItem('ford_access_requests') || '[]');
            existing.push(testData);
            localStorage.setItem('ford_access_requests', JSON.stringify(existing));
            
            log('✅ Test data added to localStorage');
            document.getElementById('localstorage-results').innerHTML = '<div class="success">✅ Datos test agregados</div>';
            
            checkLocalStorage();
        }

        // Display functions
        function displayNotifications(notifications) {
            const container = document.getElementById('notifications-display');
            
            if (!notifications || notifications.length === 0) {
                container.innerHTML = '<div class="info">No hay notificaciones para mostrar</div>';
                return;
            }
            
            const html = notifications.map(notification => `
                <div class="notification-item">
                    <strong>${notification.empleado_nombre}</strong> (${notification.empleado_cdsid})<br>
                    <small>Región: ${notification.region} | Prioridad: ${notification.prioridad} | Estado: ${notification.estado}</small><br>
                    <small>Creado: ${new Date(notification.created_at).toLocaleString()}</small><br>
                    <small>Plataformas: ${notification.plataformas_faltantes ? notification.plataformas_faltantes.join(', ') : 'N/A'}</small>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Debug page initialized');
            
            // Update system status
            document.getElementById('system-status').innerHTML = `
                <p>✅ Debug page loaded</p>
                <p>🔗 Supabase URL: ${SUPABASE_URL}</p>
                <p>🌐 Portal URL: ${PORTAL_URL}</p>
                <p>⏰ Timestamp: ${new Date().toLocaleString()}</p>
            `;
            
            // Auto-run basic tests
            setTimeout(() => {
                testSupabaseConnection();
                checkLocalStorage();
            }, 1000);
        });
    </script>
</body>
</html>
